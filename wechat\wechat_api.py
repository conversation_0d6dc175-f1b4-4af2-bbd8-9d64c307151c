# -*- coding: utf-8 -*-
"""
微信机器人完整API服务器
集成消息监听、发送消息、文件管理等所有功能
启动后自动开始监听，支持实时记录和控制
"""

import os
import sys
import time
import json

# 设置控制台编码为UTF-8
if sys.platform.startswith('win'):
    import locale
    import codecs
    # 设置标准输出编码
    try:
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
        sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())
    except:
        pass
    # 设置环境编码
    os.environ['PYTHONIOENCODING'] = 'utf-8'

import json
import time
import threading
import logging
import traceback
import random
import re
import tempfile  # 临时文件
import shutil    # 文件操作
import urllib.parse
import urllib.request
from datetime import datetime
from collections import deque, defaultdict
import pythoncom
import requests
import shutil
from flask import Flask, request, jsonify
from flask_cors import CORS


# 设置环境变量
os.environ["PROJECT_NAME"] = 'iwyxdxl/WeChatBot_WXAUTO_SE'
from wxautox_wechatbot.param import WxParam
WxParam.ENABLE_FILE_LOGGER = False

class SafeFileWriter:
    """安全文件写入器 - 防止并发写入冲突"""
    _file_locks = {}  # 文件锁字典
    _lock = threading.Lock()  # 保护文件锁字典的锁

    @classmethod
    def get_file_lock(cls, file_path):
        """获取文件对应的锁"""
        with cls._lock:
            if file_path not in cls._file_locks:
                cls._file_locks[file_path] = threading.Lock()
            return cls._file_locks[file_path]

    @staticmethod
    def safe_write_json(file_path, data, max_retries=3, retry_delay=0.1):
        """安全写入JSON文件，使用文件锁和重试机制"""
        # 获取文件专用锁
        file_lock = SafeFileWriter.get_file_lock(file_path)

        with file_lock:  # 确保同一文件的写入操作串行化
            for attempt in range(max_retries):
                try:
                    # 确保目录存在
                    os.makedirs(os.path.dirname(file_path), exist_ok=True)

                    # 使用临时文件进行原子写入
                    temp_file = file_path + f'.tmp.{os.getpid()}.{int(time.time() * 1000)}'

                    try:
                        # 写入数据到临时文件
                        with open(temp_file, 'w', encoding='utf-8') as f:
                            json.dump(data, f, ensure_ascii=False, indent=2)

                        # 原子性重命名
                        if os.path.exists(file_path):
                            if os.name == 'nt':  # Windows
                                # Windows需要先删除目标文件
                                backup_file = file_path + '.backup'
                                if os.path.exists(backup_file):
                                    os.remove(backup_file)
                                shutil.move(file_path, backup_file)

                        shutil.move(temp_file, file_path)
                        return True

                    except Exception as e:
                        # 清理临时文件
                        if os.path.exists(temp_file):
                            try:
                                os.remove(temp_file)
                            except:
                                pass
                        raise e

                except Exception as e:
                    if attempt == max_retries - 1:
                        print(f"文件写入失败，已重试{max_retries}次: {e}")
                        return False
                    else:
                        print(f"文件写入失败，第{attempt+1}次重试: {e}")
                        time.sleep(retry_delay * (attempt + 1))

            return False

    @staticmethod
    def safe_read_json(file_path, default_value=None):
        """安全读取JSON文件，支持备份恢复"""
        if not os.path.exists(file_path):
            return default_value if default_value is not None else []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, Exception) as e:
            print(f"读取JSON文件失败: {e}，尝试从备份恢复")

            # 尝试从备份文件恢复
            backup_file = file_path + '.backup'
            if os.path.exists(backup_file):
                try:
                    with open(backup_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"从备份文件恢复成功: {backup_file}")
                    # 恢复主文件
                    SafeFileWriter.safe_write_json(file_path, data)
                    return data
                except Exception as backup_e:
                    print(f"备份文件也损坏: {backup_e}")

            return default_value if default_value is not None else []

    @staticmethod
    def create_backup(file_path):
        """创建文件备份"""
        if os.path.exists(file_path):
            backup_file = file_path + '.backup'
            try:
                shutil.copy2(file_path, backup_file)
                return True
            except Exception as e:
                print(f"创建备份失败: {e}")
                return False
        return False

class WeChatLogger:
    """微信适配器专用日志系统 - 精简轻量化"""

    def __init__(self):
        # 配置图标
        self.icons = {
            'info': '[i]',
            'success': '[+]',
            'warning': '[!]',
            'error': '[x]',
            'debug': '[*]',
            'system': '[#]',
            'listener': '[L]',
            'ai': '[AI]',
            'message': '[MSG]',
            'file': '[FILE]'
        }

        # 禁用原有的logging配置，避免重复输出
        logging.getLogger().handlers.clear()

        # 设置文件日志
        file_handler = logging.FileHandler('wechat_api.log', encoding='utf-8')
        file_handler.setFormatter(logging.Formatter('%(message)s'))

        self._logger = logging.getLogger(__name__)
        self._logger.setLevel(logging.INFO)
        self._logger.addHandler(file_handler)
        self._logger.propagate = False  # 防止传播到根logger

    def _format_log(self, level, message, component=None):
        """格式化日志输出"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        icon = self.icons.get(level, '[?]')
        comp = f"[{component}]" if component else "[微信适配器]"
        return f"[{timestamp}] {icon} {comp} {message}"

    def _output(self, formatted_message):
        """统一输出方法"""
        # 控制台输出
        print(formatted_message)
        # 文件输出
        self._logger.info(formatted_message)

    # 精简的便捷方法
    def info(self, message, component=None):
        self._output(self._format_log('info', message, component))

    def success(self, message, component=None):
        self._output(self._format_log('success', message, component))

    def warning(self, message, component=None):
        self._output(self._format_log('warning', message, component))

    def error(self, message, component=None):
        self._output(self._format_log('error', message, component))

    def debug(self, message, component=None):
        # debug信息只输出到文件，不输出到控制台
        self._logger.info(self._format_log('debug', message, component))

    def system(self, message, component=None):
        self._output(self._format_log('system', message, component))

    def listener(self, message, component=None):
        self._output(self._format_log('listener', message, component))

    def ai(self, message, component=None):
        self._output(self._format_log('ai', message, component))

    def message(self, message, component=None):
        self._output(self._format_log('message', message, component))

    def file(self, message, component=None):
        self._output(self._format_log('file', message, component))

# 创建全局日志实例
logger = WeChatLogger()

# 初始化Flask应用
app = Flask(__name__)
CORS(app)

# 默认配置
DEFAULT_CONFIG = {
    "LISTEN_LIST": [],  # 格式: [{"name": "用户名", "type": "private/group"}]
    "WEB_PORT": 7702,
    "LOG_DIR": "chat_logs",
    "AUTO_START_LISTENING": True  # 启动后自动开始监听
}

# 全局变量
wx = None
ROBOT_WX_NAME = None
bot_running = False
listener_status = {}
listener_lock = threading.Lock()

# 消息缓存系统
message_cache = defaultdict(lambda: {
    'messages': deque(),
    'timer': None,
    'processing': False,
    'wait_cycles': 0,
    'created_time': time.time(),
    'last_activity': time.time(),
    'total_messages': 0,
    'processed_batches': 0
})
cache_lock = threading.Lock()

# 缓存统计
cache_stats = {
    'total_messages_cached': 0,
    'total_batches_processed': 0,
    'total_messages_merged': 0,
    'cache_hit_rate': 0.0,
    'average_batch_size': 0.0,
    'start_time': time.time()
}
stats_lock = threading.Lock()

# WebSocket事件推送配置
WEBHOOK_URL = None  # 可以配置webhook URL来推送事件

def send_event_to_webhook(event_data):
    """发送事件到webhook或直接输出JSON"""
    try:
        # 处理路径对象，确保可以JSON序列化
        if 'file_path' in event_data and event_data['file_path'] and hasattr(event_data['file_path'], '__fspath__'):
            event_data['file_path'] = str(event_data['file_path'])

        # 输出JSON格式的事件数据到stdout，供管理面板接收
        json_output = json.dumps({
            "type": "wechat_event",
            "timestamp": datetime.now().isoformat(),
            "data": event_data
        }, ensure_ascii=False)

        # 使用特殊标记包围JSON输出，便于解析
        print(f"WECHAT_EVENT_START{json_output}WECHAT_EVENT_END", flush=True)

    except Exception as e:
        logger.error(f"发送事件失败: {e}", "事件推送")

def sync_bot_sent_file_to_download_dir(file_path):
    """
    bot发送文件后，同步保存到wxautox文件下载目录
    只处理非图片文件，图片文件不需要同步
    """
    try:
        if not file_path or not os.path.exists(file_path):
            return False

        # 获取文件扩展名，判断是否为图片
        file_ext = os.path.splitext(file_path)[1].lower()
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico', '.tiff', '.tif'}

        # 如果是图片文件，不需要同步
        if file_ext in image_extensions:
            logger.debug(f"跳过图片文件同步: {file_path}", "文件同步")
            return False

        # 创建wxautox文件下载目录
        download_dir = os.path.join(os.path.dirname(__file__), "wxautox文件下载")
        os.makedirs(download_dir, exist_ok=True)

        # 获取原文件名
        filename = os.path.basename(file_path)
        target_path = os.path.join(download_dir, filename)

        # 如果目标文件已存在，生成新的文件名
        if os.path.exists(target_path):
            name, ext = os.path.splitext(filename)
            counter = 1
            while os.path.exists(target_path):
                new_filename = f"{name}_{counter}{ext}"
                target_path = os.path.join(download_dir, new_filename)
                counter += 1

        # 复制文件到下载目录
        shutil.copy2(file_path, target_path)
        logger.success(f"bot发送文件已同步到下载目录: {target_path}", "文件同步")
        return True

    except Exception as e:
        logger.error(f"同步bot发送文件失败: {e}", "文件同步")
        return False

# 配置管理
def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 合并默认配置
        for key, value in DEFAULT_CONFIG.items():
            if key not in config:
                config[key] = value

        # 转换旧格式的LISTEN_LIST
        if config.get('LISTEN_LIST') and isinstance(config['LISTEN_LIST'][0], str):
            old_list = config['LISTEN_LIST']
            config['LISTEN_LIST'] = [{"name": name, "type": "private"} for name in old_list]
            save_config(config)
            logger.info("已转换旧格式的监听列表", "配置系统")

        # 确保有默认的AI配置
        if not config.get('AI_AUTO_REPLY'):
            config['AI_AUTO_REPLY'] = {
                'enabled': False,
                'debug': False,
                'api_url': 'http://localhost:6005/v1/chat/completions',
                'api_key': '114514',
                'model': 'gemini-2.5-pro-free',
                'type': 'mcp',
                'enable_context': True,
                'memory_tracking': True,
                'maxContextSize': 20,
                'render_as_image': False,
                'useragent': 'ai_auto_reply',
                'assistantName': 'AI助手',
                'agent': 'AI助手',
                'assistant_id': 2857896171,
                'user_id': 2166683295,
                # 新增：科学算法参数配置
                'scientific_algorithms': {
                    'enabled': True,
                    'emotion_analysis': {
                        'russell_model': True,  # Russell环状模型
                        'pad_model': True,      # PAD情感空间理论
                        'plutchik_wheel': True  # Plutchik情感轮盘
                    },
                    'stress_analysis': {
                        'yerkes_dodson': True,      # Yerkes-Dodson定律
                        'gas_theory': True,         # 一般适应综合症
                        'lazarus_appraisal': True   # Lazarus认知评价理论
                    },
                    'relationship_analysis': {
                        'sternberg_theory': True,   # 三元爱情理论
                        'levinger_stages': True,    # 关系发展阶段
                        'social_penetration': True  # 社交渗透理论
                    },
                    'cognition_analysis': {
                        'dawkins_memes': True,      # Dawkins模因理论
                        'cognitive_load': True,     # 认知负荷理论
                        'bandura_theory': True      # Bandura社会认知理论
                    },
                },
                # 私聊模式配置
                'private_chat': {
                    'enabled': True,
                    'model': 'gemini-2.5-pro-free',
                    'assistantName': '雨安安',
                    'agent': '雨安安',
                    'useragent': '静',  # 私聊中的用户标识
                    'assistant_id': 2857896171,
                    'user_id': 2166683295,
                    'render_as_image': False,
                    'reply_probability': 0.8,
                    'trigger_keywords': [],
                    'keyword_must_trigger': False,
                    'target_users': [],
                    'private_history_count': 5  # 私聊历史记录数量
                },
                # 群聊模式配置
                'group_chat': {
                    'enabled': True,
                    'model': 'gemini-2.5-pro-free',
                    'assistantName': '雨安安',
                    'agent': '雨安安',
                    # 群聊中useragent使用发送者名称，不在配置中设置
                    'assistant_id': 2857896171,
                    'user_id': 2166683295,
                    'render_as_image': False,
                    'reply_probability': 0.3,
                    'trigger_keywords': ['雨安安'],
                    'keyword_must_trigger': True,
                    'target_users': [],
                    'group_history_count': 10  # 群聊历史记录数量
                },
                'reply_delay': 2000,
                'min_message_length': 1,
                'exclude_bot_names': ['AI助手', 'self'],
                'auto_filter_bots': True,
                'enable_segmented_reply': True,
                'max_segments': 5,
                'punctuation_priority': True,
                'custom_separators': '',
                'segment_delay': 1.0,
                'api_timeout': 30,
                'api_retry_count': 3
            }
            save_config(config)
            logger.info("已添加默认AI配置", "配置系统")
        else:
            # 确保现有配置包含所有新字段
            ai_config = config['AI_AUTO_REPLY']
            default_ai_fields = {
                'render_as_image': False,
                'useragent': 'ai_auto_reply',
                'custom_separators': '',
                'api_timeout': 30,
                'api_retry_count': 3,
                'assistant_id': 2857896171,
                'user_id': 2166683295,
                'private_chat': {
                    'reply_probability': 0.8,
                    'trigger_keywords': [],
                    'keyword_must_trigger': False,
                    'target_users': [],
                    'private_history_count': 5  # 私聊历史记录数量
                },
                'group_chat': {
                    'reply_probability': 0.3,
                    'trigger_keywords': ['AI助手'],
                    'keyword_must_trigger': True,
                    'target_users': [],
                    'group_history_count': 10  # 群聊历史记录数量
                }
            }

            updated = False
            for field, default_value in default_ai_fields.items():
                if field not in ai_config:
                    ai_config[field] = default_value
                    updated = True

            # 迁移旧配置到新的分离模式
            if 'reply_probability' in ai_config and 'private_chat' in ai_config:
                if 'reply_probability' not in ai_config['private_chat']:
                    ai_config['private_chat']['reply_probability'] = ai_config.get('reply_probability', 0.8)
                if 'trigger_keywords' not in ai_config['private_chat']:
                    ai_config['private_chat']['trigger_keywords'] = ai_config.get('trigger_keywords', [])
                if 'keyword_must_trigger' not in ai_config['private_chat']:
                    ai_config['private_chat']['keyword_must_trigger'] = ai_config.get('keyword_must_trigger', False)
                if 'target_users' not in ai_config['private_chat']:
                    ai_config['private_chat']['target_users'] = ai_config.get('target_users', [])
                updated = True

            if updated:
                save_config(config)
                logger.info("已更新AI配置，添加新字段并迁移旧配置", "配置系统")

        return config
    except Exception as e:
        logger.error(f"加载配置失败: {e}", "配置系统")
        # 返回包含完整AI配置的默认配置
        default_config = DEFAULT_CONFIG.copy()
        default_config['AI_AUTO_REPLY'] = {
            'enabled': False,
            'debug': False,
            'api_url': 'http://localhost:6005/v1/chat/completions',
            'api_key': '114514',
            'model': 'gemini-2.5-pro-free',
            'type': 'mcp',
            'enable_context': True,
            'memory_tracking': True,
            'maxContextSize': 20,
            'render_as_image': False,
            'useragent': 'ai_auto_reply',
            'assistantName': 'AI助手',
            'agent': '助手',
            'reply_probability': 0.8,
            'reply_delay': 2000,
            'min_message_length': 1,
            'target_users': [],
            'exclude_bot_names': ['雨安', 'self'],
            'trigger_keywords': [],
            'keyword_must_trigger': True,
            'auto_filter_bots': True,
            'enable_segmented_reply': True,
            'max_segments': 5,
            'punctuation_priority': True,
            'custom_separators': '',
            'segment_delay': 1.0,
            'api_timeout': 30,
            'api_retry_count': 3
        }
        return default_config

def save_config(config):
    """保存配置文件"""
    try:
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        logger.error(f"保存配置失败: {e}", "配置系统")
        return False

# 配置文件监控和热更新
class ConfigHotReloader:
    """配置文件热更新管理器"""

    def __init__(self):
        self.config_file = 'config.json'
        self.last_modified = 0
        self.check_interval = 2  # 检查间隔（秒）
        self.is_running = False
        self.monitor_thread = None
        self.is_reloading = False  # 标志：是否正在重新加载配置
        self.reload_debounce_time = 3  # 重新加载后的防抖时间（秒）

        # 获取初始修改时间
        try:
            self.last_modified = os.path.getmtime(self.config_file)
        except:
            self.last_modified = 0

    def start_monitoring(self):
        """启动配置文件监控"""
        if self.is_running:
            return

        self.is_running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.success("配置文件热更新监控已启动", "配置热更新")

    def stop_monitoring(self):
        """停止配置文件监控"""
        self.is_running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        logger.info("配置文件热更新监控已停止", "配置热更新")

    def _monitor_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                # 如果正在重新加载配置，跳过检测
                if self.is_reloading:
                    time.sleep(self.check_interval)
                    continue

                if os.path.exists(self.config_file):
                    current_modified = os.path.getmtime(self.config_file)
                    if current_modified > self.last_modified:
                        self.last_modified = current_modified

                        logger.info("检测到配置文件变化，开始热更新...", "配置热更新")

                        # 设置重新加载标志，防止循环触发
                        self.is_reloading = True

                        try:
                            # 延迟一点时间确保文件写入完成
                            time.sleep(0.5)

                            # 执行热更新
                            self._reload_config_and_update_listeners()

                            # 重新加载完成后，更新修改时间并等待防抖时间
                            if os.path.exists(self.config_file):
                                self.last_modified = os.path.getmtime(self.config_file)

                            # 防抖等待，避免连续触发
                            logger.debug(f"热更新完成，等待{self.reload_debounce_time}秒防抖时间", "配置热更新")
                            time.sleep(self.reload_debounce_time)

                        finally:
                            # 清除重新加载标志
                            self.is_reloading = False

                time.sleep(self.check_interval)

            except Exception as e:
                logger.error(f"配置文件监控异常: {e}", "配置热更新")
                self.is_reloading = False  # 确保异常时也清除标志
                time.sleep(self.check_interval)

    def _reload_config_and_update_listeners(self):
        """重新加载配置并更新监听器"""
        global config
        try:
            # 保存旧的监听列表
            old_listen_list = config.get('LISTEN_LIST', [])
            old_listeners = {listener['name']: listener for listener in old_listen_list}

            # 重新加载配置 - 使用只读方式，不修改文件
            new_config = self._load_config_readonly()
            if new_config:
                config = new_config
                new_listen_list = config.get('LISTEN_LIST', [])
                new_listeners = {listener['name']: listener for listener in new_listen_list}

                logger.info(f"配置重新加载完成，旧监听器: {len(old_listeners)}个，新监听器: {len(new_listeners)}个", "配置热更新")

                # 如果Bot正在运行，同步更新监听器
                if bot_running and wx:
                    self.sync_listeners(old_listeners, new_listeners)
                else:
                    logger.info("Bot未运行，监听器将在下次启动时生效", "配置热更新")
            else:
                logger.warning("配置文件读取失败，跳过本次热更新", "配置热更新")

        except Exception as e:
            logger.error(f"配置热更新失败: {e}", "配置热更新")

    def _load_config_readonly(self):
        """只读方式加载配置文件，不进行自动更新"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except Exception as e:
            logger.error(f"只读加载配置文件失败: {e}", "配置热更新")
            return None

    def sync_listeners(self, old_listeners, new_listeners):
        """同步监听器状态"""
        try:
            # 找出需要移除的监听器
            to_remove = set(old_listeners.keys()) - set(new_listeners.keys())
            # 找出需要添加的监听器
            to_add = set(new_listeners.keys()) - set(old_listeners.keys())
            # 找出类型发生变化的监听器
            to_update = []
            for name in set(old_listeners.keys()) & set(new_listeners.keys()):
                if old_listeners[name].get('type') != new_listeners[name].get('type'):
                    to_update.append(name)

            logger.info(f"监听器同步: 移除{len(to_remove)}个, 添加{len(to_add)}个, 更新{len(to_update)}个", "配置热更新")

            # 移除不再需要的监听器
            for user_name in to_remove:
                try:
                    wx.RemoveListenChat(nickname=user_name)
                    logger.success(f"已移除监听器: {user_name}", "配置热更新")

                    # 移除监听状态
                    with listener_lock:
                        if user_name in listener_status:
                            del listener_status[user_name]

                except Exception as e:
                    logger.error(f"移除监听器失败 {user_name}: {e}", "配置热更新")

            # 更新类型发生变化的监听器
            for user_name in to_update:
                try:
                    # 先移除再添加
                    wx.RemoveListenChat(nickname=user_name)
                    wx.AddListenChat(nickname=user_name, callback=message_listener)

                    # 更新监听状态
                    with listener_lock:
                        if user_name in listener_status:
                            listener_status[user_name]['user_type'] = new_listeners[user_name].get('type', 'private')
                            listener_status[user_name]['status'] = 'listening'

                    logger.success(f"已更新监听器: {user_name} -> {new_listeners[user_name].get('type')}", "配置热更新")

                except Exception as e:
                    logger.error(f"更新监听器失败 {user_name}: {e}", "配置热更新")

            # 添加新的监听器
            for user_name in to_add:
                try:
                    listener = new_listeners[user_name]
                    user_type = listener.get('type', 'private')

                    wx.AddListenChat(nickname=user_name, callback=message_listener)
                    logger.success(f"已添加监听器: {user_name} ({user_type})", "配置热更新")

                    # 初始化监听状态
                    current_time = datetime.now()
                    with listener_lock:
                        listener_status[user_name] = {
                            'user_name': user_name,
                            'user_type': user_type,
                            'last_message_time': None,
                            'last_message_content': '',
                            'message_count': 0,
                            'listener_added_time': current_time.isoformat(),
                            'status': 'listening',
                            'last_restore_time': None,
                            'error_count': 0
                        }

                except Exception as e:
                    logger.error(f"添加监听器失败 {user_name}: {e}", "配置热更新")

                    # 记录错误状态
                    current_time = datetime.now()
                    with listener_lock:
                        listener_status[user_name] = {
                            'user_name': user_name,
                            'user_type': new_listeners[user_name].get('type', 'private'),
                            'last_message_time': None,
                            'last_message_content': '',
                            'message_count': 0,
                            'listener_added_time': current_time.isoformat(),
                            'status': 'error',
                            'last_restore_time': None,
                            'error_count': 1
                        }

            if to_remove or to_add or to_update:
                logger.success("监听器同步完成！", "配置热更新")
            else:
                logger.info("监听器配置无变化", "配置热更新")

        except Exception as e:
            logger.error(f"监听器同步失败: {e}", "配置热更新")

# 创建全局配置热更新管理器
config_hot_reloader = ConfigHotReloader()

# 加载配置
config = load_config()

# 注意：智能对话记录功能已移至主程序统一处理，此处不再需要
# 保留此注释作为说明：对话记录由server.js中的recordEmotionMemory统一处理

# 消息记录功能 - 文件存储（原有逻辑）
def save_message_to_file_legacy(user_name, user_type, sender_name, content_data, message_type="text", file_path=None):
    """保存消息到JSON文件 - 原有逻辑"""
    try:
        # 创建日志目录结构
        log_dir = config.get("LOG_DIR", "chat_logs")

        # 根据类型创建不同的文件夹
        if user_type == "group":
            type_dir = os.path.join(log_dir, "群聊")
            chat_prefix = f"[群聊:{user_name}]"
        else:
            type_dir = os.path.join(log_dir, "私聊")
            chat_prefix = f"[{ROBOT_WX_NAME or '机器人'}和{user_name}的私聊]"

        # 创建用户文件夹
        user_dir = os.path.join(type_dir, user_name)
        os.makedirs(user_dir, exist_ok=True)

        # 按日期创建文件
        today = datetime.now().strftime("%Y-%m-%d")
        log_file = os.path.join(user_dir, f"{today}.json")

        # 格式化时间
        current_time = datetime.now()
        time_str = current_time.strftime("%m-%d %H:%M:%S")
        timestamp = current_time.isoformat()

        # 构建消息对象
        message_obj = {
            "timestamp": timestamp,
            "time_str": time_str,
            "sender": sender_name,
            "type": message_type,
            "content_data": content_data,
            "file_path": file_path
        }

        # 格式化显示内容 - 直接使用传入的content_data，它已经包含了完整的格式化信息
        display_content = f"{chat_prefix}[{time_str}] {content_data}"

        # 添加格式化的内容字段
        message_obj["content"] = display_content

        # 使用安全文件写入机制
        # 创建备份（如果文件存在）
        if os.path.exists(log_file):
            SafeFileWriter.create_backup(log_file)

        # 读取现有消息
        messages = SafeFileWriter.safe_read_json(log_file, [])

        # 添加新消息
        messages.append(message_obj)

        # 安全写入文件
        if not SafeFileWriter.safe_write_json(log_file, messages):
            logger.error(f"安全写入失败: {log_file}", "文件系统")
            return False

        logger.debug(f"消息已保存到: {log_file}")
        return True

    except Exception as e:
        logger.error(f"保存消息到文件失败: {e}", "文件系统")
        return False

# 消息文件保存接口（仅作备份，主要使用智能记忆系统）
def save_message_to_file(user_name, user_type, sender_name, content_data, message_type="text", file_path=None):
    """保存消息到文件 - 作为智能记忆系统的备份"""
    return save_message_to_file_legacy(user_name, user_type, sender_name, content_data, message_type, file_path)

# 消息缓存处理函数
def generate_cache_id(user_name, bot_name, message_type):
    """生成唯一的缓存ID"""
    return f"{message_type}_{user_name}_{bot_name}"

def add_message_to_cache(event_data):
    """将消息添加到缓存"""
    try:
        ai_config = config.get('AI_AUTO_REPLY', {})
        cache_config = ai_config.get('message_cache', {})

        if not cache_config.get('enabled', False):
            # 缓存未启用，直接处理
            handle_ai_auto_reply_direct(event_data)
            return

        user_name = event_data.get('user_name', '')
        message_type = event_data.get('message_type', 'private')
        bot_name = ROBOT_WX_NAME or "机器人"

        cache_id = generate_cache_id(user_name, bot_name, message_type)

        with cache_lock:
            cache_entry = message_cache[cache_id]

            # 添加消息到缓存
            cache_entry['messages'].append(event_data)
            cache_entry['last_activity'] = time.time()
            cache_entry['total_messages'] += 1

            # 更新全局统计
            with stats_lock:
                cache_stats['total_messages_cached'] += 1

            if cache_config.get('debug', False):
                logger.debug(f"消息已添加到缓存 {cache_id}, 当前缓存数量: {len(cache_entry['messages'])}", "消息缓存")

            # 如果正在处理中，不启动新的定时器
            if cache_entry['processing']:
                if cache_config.get('debug', False):
                    logger.debug(f"缓存 {cache_id} 正在处理中，消息已排队", "消息缓存")
                return

            # 取消之前的定时器
            if cache_entry['timer']:
                cache_entry['timer'].cancel()

            # 启动新的等待定时器
            wait_time = cache_config.get('wait_time', 5)
            cache_entry['timer'] = threading.Timer(wait_time, process_cached_messages, args=[cache_id])
            cache_entry['timer'].start()

            if cache_config.get('debug', False):
                logger.debug(f"为缓存 {cache_id} 启动 {wait_time}s 等待定时器", "消息缓存")

    except Exception as e:
        logger.error(f"添加消息到缓存失败: {e}", "消息缓存")
        # 失败时直接处理消息
        handle_ai_auto_reply_direct(event_data)

def process_cached_messages(cache_id):
    """处理缓存的消息"""
    try:
        ai_config = config.get('AI_AUTO_REPLY', {})
        cache_config = ai_config.get('message_cache', {})

        with cache_lock:
            cache_entry = message_cache[cache_id]

            # 检查是否还有消息
            if not cache_entry['messages']:
                if cache_config.get('debug', False):
                    logger.debug(f"缓存 {cache_id} 为空，跳过处理", "消息缓存")
                return

            # 检查等待周期
            cache_entry['wait_cycles'] += 1
            max_cycles = cache_config.get('max_wait_cycles', 3)

            # 如果还在等待周期内且有新消息，继续等待
            if cache_entry['wait_cycles'] < max_cycles:
                wait_time = cache_config.get('wait_time', 5)
                cache_entry['timer'] = threading.Timer(wait_time, process_cached_messages, args=[cache_id])
                cache_entry['timer'].start()

                if cache_config.get('debug', False):
                    logger.debug(f"缓存 {cache_id} 继续等待，周期 {cache_entry['wait_cycles']}/{max_cycles}", "消息缓存")
                return

            # 标记为处理中
            cache_entry['processing'] = True

            # 获取所有缓存的消息
            messages = list(cache_entry['messages'])
            cache_entry['messages'].clear()
            cache_entry['wait_cycles'] = 0

            if cache_config.get('debug', False):
                logger.debug(f"开始处理缓存 {cache_id} 中的 {len(messages)} 条消息", "消息缓存")

        # 合并消息内容
        if messages:
            # 使用最后一条消息作为基础
            merged_event = messages[-1].copy()

            # 合并所有消息内容
            merged_content = []
            for msg in messages:
                content = msg.get('content', '').strip()
                if content:
                    merged_content.append(content)

            if merged_content:
                merged_event['content'] = '\n'.join(merged_content)
                merged_event['original_message_count'] = len(messages)

                # 更新统计信息
                with stats_lock:
                    cache_stats['total_batches_processed'] += 1
                    cache_stats['total_messages_merged'] += len(messages)

                    # 计算平均批次大小
                    if cache_stats['total_batches_processed'] > 0:
                        cache_stats['average_batch_size'] = cache_stats['total_messages_merged'] / cache_stats['total_batches_processed']

                    # 计算缓存命中率（合并的消息数 / 总缓存消息数）
                    if cache_stats['total_messages_cached'] > 0:
                        cache_stats['cache_hit_rate'] = cache_stats['total_messages_merged'] / cache_stats['total_messages_cached']

                # 更新缓存条目统计
                with cache_lock:
                    cache_entry['processed_batches'] += 1

                if cache_config.get('debug', False):
                    logger.debug(f"合并了 {len(messages)} 条消息，总长度: {len(merged_event['content'])}", "消息缓存")

                # 处理合并后的消息
                handle_ai_auto_reply_direct(merged_event)

        # 处理完成，重置状态
        with cache_lock:
            cache_entry = message_cache[cache_id]
            cache_entry['processing'] = False

            # 如果处理期间又有新消息，启动新的处理周期
            if cache_entry['messages']:
                if cache_config.get('debug', False):
                    logger.debug(f"处理期间收到新消息，启动新的处理周期", "消息缓存")

                wait_time = cache_config.get('wait_time', 5)
                cache_entry['timer'] = threading.Timer(wait_time, process_cached_messages, args=[cache_id])
                cache_entry['timer'].start()

    except Exception as e:
        logger.error(f"处理缓存消息失败: {e}", "消息缓存")
        # 重置处理状态
        with cache_lock:
            if cache_id in message_cache:
                message_cache[cache_id]['processing'] = False

# AI自动回复处理函数
def handle_ai_auto_reply(event_data):
    """处理AI自动回复逻辑 - 支持消息缓存"""
    try:
        # 检查是否启用消息缓存
        ai_config = config.get('AI_AUTO_REPLY', {})
        cache_config = ai_config.get('message_cache', {})

        if cache_config.get('enabled', False):
            # 使用消息缓存
            add_message_to_cache(event_data)
        else:
            # 直接处理
            handle_ai_auto_reply_direct(event_data)

    except Exception as e:
        logger.error(f"AI自动回复处理失败: {e}", "AI系统")

def cleanup_old_cache_entries():
    """清理长时间未活动的缓存条目"""
    try:
        current_time = time.time()
        cleanup_threshold = 3600  # 1小时未活动则清理

        with cache_lock:
            cache_ids_to_remove = []

            for cache_id, cache_entry in message_cache.items():
                # 检查是否长时间未活动且未在处理中
                if (not cache_entry['processing'] and
                    current_time - cache_entry['last_activity'] > cleanup_threshold):

                    # 取消定时器
                    if cache_entry['timer']:
                        cache_entry['timer'].cancel()

                    cache_ids_to_remove.append(cache_id)

            # 移除过期的缓存条目
            for cache_id in cache_ids_to_remove:
                del message_cache[cache_id]
                logger.debug(f"清理过期缓存条目: {cache_id}", "消息缓存")

        if cache_ids_to_remove:
            logger.info(f"清理了 {len(cache_ids_to_remove)} 个过期缓存条目", "消息缓存")

    except Exception as e:
        logger.error(f"清理缓存条目失败: {e}", "消息缓存")

def get_cache_statistics():
    """获取缓存统计信息"""
    try:
        with stats_lock:
            stats = cache_stats.copy()

        with cache_lock:
            active_caches = len(message_cache)
            total_queued_messages = sum(len(entry['messages']) for entry in message_cache.values())
            processing_caches = sum(1 for entry in message_cache.values() if entry['processing'])

        stats.update({
            'active_caches': active_caches,
            'total_queued_messages': total_queued_messages,
            'processing_caches': processing_caches,
            'uptime_seconds': time.time() - stats['start_time']
        })

        return stats

    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}", "消息缓存")
        return {}

def reset_cache_statistics():
    """重置缓存统计"""
    try:
        with stats_lock:
            cache_stats.update({
                'total_messages_cached': 0,
                'total_batches_processed': 0,
                'total_messages_merged': 0,
                'cache_hit_rate': 0.0,
                'average_batch_size': 0.0,
                'start_time': time.time()
            })
        logger.info("缓存统计已重置", "消息缓存")

    except Exception as e:
        logger.error(f"重置缓存统计失败: {e}", "消息缓存")

def handle_ai_auto_reply_direct(event_data):
    """处理AI自动回复逻辑 - 支持群聊和私聊模式分离"""
    try:
        # 检查AI自动回复是否启用
        ai_config = config.get('AI_AUTO_REPLY', {})
        if not ai_config.get('enabled', False):
            return

        # 获取消息类型（群聊或私聊）
        message_type = event_data.get('message_type', 'private')

        # 根据消息类型获取对应配置
        if message_type == 'group':
            mode_config = ai_config.get('group_chat', {})
            if ai_config.get('debug', False):
                logger.debug(f"使用群聊模式配置", "AI系统")
        else:
            mode_config = ai_config.get('private_chat', {})
            if ai_config.get('debug', False):
                logger.debug(f"使用私聊模式配置", "AI系统")

        # 过滤机器人自己的消息
        exclude_bot_names = ai_config.get('exclude_bot_names', ['AI助手', 'self'])
        # 动态添加当前机器人名称
        if ROBOT_WX_NAME and ROBOT_WX_NAME not in exclude_bot_names:
            exclude_bot_names.append(ROBOT_WX_NAME)

        if event_data.get('sender_name') in exclude_bot_names:
            if ai_config.get('debug', False):
                logger.debug(f"过滤机器人消息: {event_data.get('sender_name')}", "AI系统")
            return

        # 检查目标用户（优先使用模式配置，然后是全局配置）
        target_users = mode_config.get('target_users', ai_config.get('target_users', []))
        if target_users and event_data.get('user_name') not in target_users:
            if ai_config.get('debug', False):
                logger.debug(f"用户不在目标列表: {event_data.get('user_name')}", "AI系统")
            return

        # 检查消息类型和长度
        # 移除消息类型限制，支持所有类型的消息
        # if event_data.get('content_type') not in ['text', 'quote']:
        #     return

        # 获取AI输入内容
        content_type = event_data.get('content_type', 'text')
        if content_type == 'quote':
            # 对于引用消息，直接使用完整的content字段
            ai_input_content = event_data.get('content', '')

            if ai_config.get('debug', False):
                logger.debug(f"引用消息AI输入: {ai_input_content}", "引用处理")
        else:
            # 对于普通消息，使用content
            ai_input_content = event_data.get('content', '')

            if ai_config.get('debug', False):
                logger.debug(f"普通消息AI输入: {ai_input_content}", "消息处理")

        min_length = ai_config.get('min_message_length', 1)
        if len(ai_input_content) < min_length:
            return

        # 检查关键词触发和回复概率（使用模式配置）
        trigger_keywords = mode_config.get('trigger_keywords', ai_config.get('trigger_keywords', []))
        keyword_matched = False

        # 检查关键词匹配
        if trigger_keywords:
            for keyword in trigger_keywords:
                if keyword.lower() in ai_input_content.lower():
                    keyword_matched = True
                    if ai_config.get('debug', False):
                        logger.debug(f"关键词触发: {keyword}", "AI系统")
                    break

        # 获取模式配置的关键词必须触发设置
        keyword_must_trigger = mode_config.get('keyword_must_trigger', ai_config.get('keyword_must_trigger', True))

        # 回复逻辑优化：
        # 1. 如果匹配到关键词，直接回复（100%概率）
        # 2. 如果没有关键词配置，按概率回复
        # 3. 如果有关键词配置但没匹配到：
        #    - 如果设置了必须触发关键词，则不回复
        #    - 如果没有设置必须触发关键词，则按概率回复

        should_reply = False

        if keyword_matched:
            # 匹配到关键词，必定回复
            should_reply = True
            if ai_config.get('debug', False):
                logger.debug(f"关键词匹配，必定回复", "AI系统")
        elif not trigger_keywords:
            # 没有关键词配置，按概率回复
            reply_probability = mode_config.get('reply_probability', ai_config.get('reply_probability', 1.0))
            import random
            should_reply = random.random() <= reply_probability
            if ai_config.get('debug', False):
                logger.debug(f"无关键词配置，概率回复: {reply_probability}, 结果: {should_reply}", "AI系统")
        elif not keyword_must_trigger:
            # 有关键词配置但没匹配到，且不要求必须触发关键词，按概率回复
            reply_probability = mode_config.get('reply_probability', ai_config.get('reply_probability', 1.0))
            import random
            should_reply = random.random() <= reply_probability
            if ai_config.get('debug', False):
                logger.debug(f"关键词未匹配但允许概率回复: {reply_probability}, 结果: {should_reply}", "AI系统")
        else:
            # 有关键词配置但没匹配到，且要求必须触发关键词，不回复
            should_reply = False
            if ai_config.get('debug', False):
                logger.debug(f"关键词未匹配且要求必须触发，不回复", "AI系统")

        if not should_reply:
            return

        # 在新线程中处理AI回复，避免阻塞消息监听
        threading.Thread(
            target=process_ai_reply_async,
            args=(event_data, ai_config),
            daemon=True
        ).start()

    except Exception as e:
        logger.error(f"AI自动回复处理失败: {e}", "AI系统")

def process_ai_reply_async(event_data, ai_config):
    """异步处理AI回复"""
    try:
        user_name = event_data.get('user_name')
        sender_name = event_data.get('sender_name', user_name)
        message_type = event_data.get('message_type', 'private')
        raw_content = event_data.get('content')

        # 根据消息类型获取对应配置
        if message_type == 'group':
            mode_config = ai_config.get('group_chat', {})
        else:
            mode_config = ai_config.get('private_chat', {})

        # 合并配置：模式配置优先，然后是全局配置
        merged_config = ai_config.copy()
        merged_config.update(mode_config)

        # 统一构建聊天上下文参数，只通过chatType区分
        # 统一使用发送者名称作为useragent
        merged_config['useragent'] = sender_name

        # 获取助手名称
        assistant_name = merged_config.get('assistantName', ROBOT_WX_NAME or 'AI助手')

        # 统一构建聊天上下文
        chat_name = user_name  # 直接使用用户名或群名
        history_count = merged_config.get(
            'history_count',
            10 if message_type == 'group' else 5
        )

        # 统一构建聊天上下文对象
        merged_config['chat_context'] = {
            'type': message_type,  # 使用message_type作为类型参数
            'name': chat_name,
            'history_count': history_count,
            'user_names': {
                sender_name: sender_name,
                'assistant': assistant_name
            }
        }

        if ai_config.get('debug', False):
            logger.debug(f"处理{message_type}消息: {user_name} - {sender_name} - {raw_content[:30]}...", "AI处理")

        # 添加回复延迟
        reply_delay = merged_config.get('reply_delay', ai_config.get('reply_delay', 2000)) / 1000.0  # 转换为秒
        time.sleep(reply_delay)

        # 调用AI API生成回复
        # 统一使用发送者名称作为用户ID
        api_user_name = sender_name

        # 直接使用原始消息，让messageProcessor统一处理历史加载
        ai_response = call_ai_api(raw_content, merged_config, api_user_name)

        if ai_response:
            ai_text = ''
            image_url = None
            
            if isinstance(ai_response, dict):
                # 新格式响应，包含文本和可能的图片
                ai_text = ai_response.get('text', '')
                image_url = ai_response.get('image_url')
            else:
                # 兼容旧格式响应
                ai_text = str(ai_response)

            if ai_config.get('debug', False):
                logger.debug(f"AI回复生成: {len(ai_text)}字符", "AI处理")

            # 发送回复消息
            send_ai_reply(user_name, ai_text, merged_config, image_url=image_url)

            # 注意：对话记录由主程序的智能记忆系统统一处理，微信适配器不再重复记录
            # 只保留文件备份记录功能
            try:
                if ai_config.get('debug', False):
                    logger.debug(f"对话记录由主程序智能记忆系统处理: {user_name}", "消息处理")
                    
                # 仅作为备份，记录到文件（可选）
                if ai_config.get('backup_to_file', False):
                    robot_name = ROBOT_WX_NAME or "机器人"
                    save_message_to_file(user_name, message_type, sender_name, raw_content, "text")
                    save_message_to_file(user_name, message_type, robot_name, ai_text, "text")
                    logger.debug("已备份到文件", "消息记录")
                    
            except Exception as backup_error:
                logger.error(f"文件备份失败: {backup_error}", "消息记录")

    except Exception as e:
        logger.error(f"异步AI回复处理失败: {e}", "AI处理")
        import traceback
        logger.debug(f"AI处理错误详情: {traceback.format_exc()}", "AI处理")

def call_ai_api(user_message, ai_config, user_name=None):
    """调用AI API生成回复 - 支持用户上下文和配置化参数"""
    try:
        # 构建用户ID
        user_id = ai_config.get('userId', user_name or 'ai_auto_reply')
        if user_name and user_name != user_id:
            user_id = f"{user_name}_wechat"

        # 获取useragent配置
        useragent = ai_config.get('useragent', user_id)

        # 构建请求体
        request_body = {
            "model": ai_config.get('model', 'gemini-2.5-pro-free'),
            "type": ai_config.get('type', 'mcp'),
            "enable_context": ai_config.get('enable_context', True),
            "memory_tracking": ai_config.get('memory_tracking', True),
            "maxContextSize": ai_config.get('maxContextSize', 20),
            "render_as_image": ai_config.get('render_as_image', False),
            "userId": user_id,
            "useragent": useragent,
            "agent": ai_config.get('agent', ROBOT_WX_NAME or 'AI助手'),
            "assistantName": ai_config.get('assistantName', ROBOT_WX_NAME or 'AI助手'),
            "assistant_id": ai_config.get('assistant_id', 999999),
            "user_id": ai_config.get('user_id', 999999),
            "censor_vcp_output": ai_config.get('censor_vcp_output', True),
            "vcp_content_max_length": ai_config.get('vcp_content_max_length', 100),
            "theme": ai_config.get('theme', 'dark'),
            "stream": ai_config.get('stream', False),
            "messages": [
                {"role": "user", "content": user_message}
            ]
        }
        
        # 添加科学算法配置
        scientific_config = ai_config.get('scientific_algorithms', {})
        if scientific_config.get('enabled', True):
            request_body['scientific_algorithms'] = scientific_config
            
            if ai_config.get('debug', False):
                logger.debug(f"启用科学算法分析: {list(scientific_config.keys())}", "AI API")

        # 添加聊天上下文参数（新格式）
        if ai_config.get('chat_context'):
            request_body['chat_context'] = ai_config.get('chat_context')

        # 发送请求 - 使用配置的超时时间和重试机制
        api_url = ai_config.get('api_url', 'http://localhost:6005/v1/chat/completions')
        api_key = ai_config.get('api_key', '114514')
        api_timeout = ai_config.get('api_timeout', 30)
        api_retry_count = ai_config.get('api_retry_count', 3)

        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}'
        }

        if ai_config.get('debug', False):
            logger.debug(f"发送AI请求: {user_id}", "AI API")

        # 重试机制
        last_error = None
        for attempt in range(api_retry_count):
            try:
                if ai_config.get('debug', False) and attempt > 0:
                    logger.debug(f"AI API重试: 第{attempt + 1}次", "AI API")

                response = requests.post(api_url, json=request_body, headers=headers, timeout=api_timeout)

                if response.status_code == 200:
                    data = response.json()

                    if data.get('choices') and len(data['choices']) > 0:
                        ai_reply = data['choices'][0]['message']['content'].strip()
                        image_url = data.get('image_url')  # 获取图片URL

                        if ai_config.get('debug', False):
                            logger.debug(f"AI回复成功: {len(ai_reply)}字符", "AI API")

                        return {
                            'text': ai_reply,
                            'image_url': image_url,
                            'raw_response': data
                        }

                logger.error(f"AI API调用失败 (尝试 {attempt + 1}): {response.status_code} - {response.text}", "AI API")
                last_error = f"HTTP {response.status_code}: {response.text}"

            except requests.exceptions.Timeout as e:
                last_error = f"请求超时 (超时时间: {api_timeout}秒)"
                logger.error(f"AI API请求超时 (尝试 {attempt + 1}): {e}", "AI API")
            except requests.exceptions.RequestException as e:
                last_error = f"请求异常: {str(e)}"
                logger.error(f"AI API请求异常 (尝试 {attempt + 1}): {e}", "AI API")
            except Exception as e:
                last_error = f"未知错误: {str(e)}"
                logger.error(f"AI API调用失败 (尝试 {attempt + 1}): {e}", "AI API")

            # 如果不是最后一次尝试，等待一段时间再重试
            if attempt < api_retry_count - 1:
                retry_delay = 2 ** attempt  # 指数退避: 1s, 2s, 4s...
                time.sleep(retry_delay)

        logger.error(f"AI API调用最终失败，已尝试 {api_retry_count} 次: {last_error}", "AI API")
        return None

    except Exception as e:
        logger.error(f"调用AI API失败: {e}", "AI API")
        import traceback
        logger.debug(f"AI API错误详情: {traceback.format_exc()}", "AI API")
        return None

def send_ai_reply(user_name, message, ai_config, image_url=None):
    """发送AI回复消息，支持代码块处理、图片发送和智能分段发送"""
    try:
        if not wx or not hasattr(wx, 'SendMsg'):
            logger.error("微信对象未初始化，无法发送AI回复", "AI回复")
            return

        # 1. 处理AI渲染的图片
        if image_url and ai_config.get('render_as_image', False):
            try:
                image_path = download_and_save_image(image_url, user_name)
                if image_path:
                    wx.SendFiles(image_path, who=user_name)
                    logger.success(f"AI图片发送成功: {user_name}", "AI回复")
                    return  # 如果是图片渲染，只发送图片，不发送文本
            except Exception as e:
                logger.error(f"发送AI渲染图片失败: {e}", "AI回复")

        # 收集所有多媒体内容，稍后统一发送
        multimedia_queue = []

        # 2. 提取和处理代码块
        code_blocks = extract_code_blocks(message)
        if code_blocks:
            # 创建代码文件并加入队列
            code_file_path = create_code_file(code_blocks, user_name)
            if code_file_path:
                multimedia_queue.append(('file', code_file_path))

        # 3. 提取和处理Markdown图片
        image_urls = extract_markdown_images(message)
        if config.get('AI_AUTO_REPLY', {}).get('debug', False):
            logger.debug(f"准备处理 {len(image_urls)} 个图片链接: {image_urls}", "AI回复")

        for idx, img_url in enumerate(image_urls):
            try:
                if config.get('AI_AUTO_REPLY', {}).get('debug', False):
                    logger.debug(f"正在下载第 {idx+1} 个图片: {img_url}", "AI回复")

                image_path = download_and_save_image(img_url, user_name)
                if image_path:
                    multimedia_queue.append(('image', image_path))
                    if config.get('AI_AUTO_REPLY', {}).get('debug', False):
                        logger.debug(f"图片 {idx+1} 已加入队列: {image_path}", "AI回复")
                else:
                    logger.warning(f"图片 {idx+1} 下载失败: {img_url}", "AI回复")
            except Exception as e:
                logger.error(f"处理图片链接 {idx+1} 失败: {e}", "AI回复")

        # 4. 清理消息文本（移除代码块和图片标记）
        cleaned_message = clean_message_content(message)

        # 5. 优先发送文本内容（如果存在）
        if cleaned_message.strip():
            # 获取分段配置（支持新旧格式）
            segmented_config = ai_config.get('segmented_reply', {})

            # 向后兼容旧配置格式
            if not segmented_config:
                segmented_config = {
                    'enabled': ai_config.get('enable_segmented_reply', False),
                    'min_length': 50,
                    'max_segments': ai_config.get('max_segments', 5),
                    'target_length': 100,
                    'min_segment_length': 30,
                    'delay': ai_config.get('segment_delay', 1.0),
                    'separators': {
                        'custom': [sep.strip() for sep in ai_config.get('custom_separators', '').split(',') if sep.strip()],
                        'sentence_endings': ['。', '！', '？', '.', '!', '?'],
                        'clause_separators': ['；', ';', '：', ':'],
                        'comma_separators': ['，', ',', '、'],
                        'line_separators': ['\n\n', '\n']
                    }
                }

            enable_segmented = segmented_config.get('enabled', False)
            min_length = segmented_config.get('min_length', 50)

            if enable_segmented and len(cleaned_message) > min_length:
                # 智能分段发送
                segments = advanced_split_message(cleaned_message, segmented_config)

                segment_delay = segmented_config.get('delay', 1.0)
                for i, segment in enumerate(segments):
                    if i > 0:
                        # 添加随机延迟，模拟真人打字
                        base_delay = segment_delay
                        random_delay = base_delay * (0.5 + random.random())
                        time.sleep(random_delay)

                    wx.SendMsg(segment, who=user_name)

                logger.success(f"AI分段回复发送成功: {user_name} ({len(segments)}段)", "AI回复")
            else:
                # 普通发送
                wx.SendMsg(cleaned_message, who=user_name)
                logger.success(f"AI回复发送成功: {user_name}", "AI回复")

        # 6. 最后发送多媒体内容
        if multimedia_queue:
            if config.get('AI_AUTO_REPLY', {}).get('debug', False):
                logger.debug(f"准备发送 {len(multimedia_queue)} 个多媒体文件", "AI回复")
                for i, (media_type, media_path) in enumerate(multimedia_queue):
                    logger.debug(f"队列 {i+1}: {media_type} - {media_path}", "AI回复")

            # 添加延迟，确保文本先到达
            time.sleep(0.5)

            for idx, (media_type, media_path) in enumerate(multimedia_queue):
                try:
                    if config.get('AI_AUTO_REPLY', {}).get('debug', False):
                        logger.debug(f"正在发送第 {idx+1} 个{media_type}: {media_path}", "AI回复")

                    wx.SendFiles(media_path, who=user_name)
                    if media_type == 'image':
                        logger.success(f"AI图片发送成功 ({idx+1}/{len(multimedia_queue)}): {user_name}", "AI回复")
                    elif media_type == 'file':
                        logger.success(f"AI文件发送成功 ({idx+1}/{len(multimedia_queue)}): {user_name}", "AI回复")
                        # bot发送文件后，同步保存到wxautox文件下载目录
                        sync_bot_sent_file_to_download_dir(media_path)

                    # 多媒体之间的延迟
                    time.sleep(0.3)
                except Exception as e:
                    logger.error(f"发送第 {idx+1} 个{media_type}失败: {e}", "AI回复")

        # 注意：AI回复的记录已在process_ai_reply_async中统一处理，这里不再重复记录

    except Exception as e:
        logger.error(f"发送AI回复失败: {e}", "AI回复")
        import traceback
        logger.debug(f"AI回复错误详情: {traceback.format_exc()}", "AI回复")

def extract_code_blocks(message):
    """提取消息中的代码块"""
    try:
        # 匹配 ```语言\n代码\n``` 格式的代码块
        code_pattern = r'```(\w*)\n?(.*?)\n?```'
        matches = re.findall(code_pattern, message, re.DOTALL)

        code_blocks = []
        for match in matches:
            language = match[0] if match[0] else 'text'
            code = match[1].strip()
            if code:  # 只处理非空代码块
                code_blocks.append({
                    'language': language,
                    'code': code
                })

        if code_blocks and config.get('AI_AUTO_REPLY', {}).get('debug', False):
            logger.debug(f"提取到 {len(code_blocks)} 个代码块", "代码处理")

        return code_blocks
    except Exception as e:
        logger.error(f"提取代码块失败: {e}", "代码处理")
        return []

def create_code_file(code_blocks, user_name):
    """创建代码文件并返回文件路径"""
    try:
        if not code_blocks:
            return None

        # 创建临时目录
        temp_dir = os.path.join("temp", "code_files")
        os.makedirs(temp_dir, exist_ok=True)

        # 生成文件名 - 根据代码类型确定扩展名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 根据代码块数量和类型确定文件扩展名
        if len(code_blocks) > 1:
            # 多个代码块时，统一使用txt格式
            ext = 'txt'
        elif len(code_blocks) == 1:
            # 单个代码块时，根据语言类型确定扩展名
            first_lang = code_blocks[0].get('language', '').lower()

            # 编程语言
            if first_lang in ['python', 'py']:
                ext = 'py'
            elif first_lang in ['javascript', 'js']:
                ext = 'js'
            elif first_lang in ['typescript', 'ts']:
                ext = 'ts'
            elif first_lang in ['java']:
                ext = 'java'
            elif first_lang in ['c', 'c++', 'cpp', 'cxx']:
                ext = 'cpp'
            elif first_lang in ['c#', 'csharp', 'cs']:
                ext = 'cs'
            elif first_lang in ['php']:
                ext = 'php'
            elif first_lang in ['ruby', 'rb']:
                ext = 'rb'
            elif first_lang in ['go', 'golang']:
                ext = 'go'
            elif first_lang in ['rust', 'rs']:
                ext = 'rs'
            elif first_lang in ['swift']:
                ext = 'swift'
            elif first_lang in ['kotlin', 'kt']:
                ext = 'kt'
            elif first_lang in ['scala']:
                ext = 'scala'
            elif first_lang in ['r']:
                ext = 'r'
            elif first_lang in ['matlab', 'm']:
                ext = 'm'
            elif first_lang in ['perl', 'pl']:
                ext = 'pl'
            elif first_lang in ['lua']:
                ext = 'lua'
            elif first_lang in ['dart']:
                ext = 'dart'

            # Web技术
            elif first_lang in ['html', 'htm']:
                ext = 'html'
            elif first_lang in ['css']:
                ext = 'css'
            elif first_lang in ['scss', 'sass']:
                ext = 'scss'
            elif first_lang in ['less']:
                ext = 'less'
            elif first_lang in ['vue']:
                ext = 'vue'
            elif first_lang in ['jsx']:
                ext = 'jsx'
            elif first_lang in ['tsx']:
                ext = 'tsx'

            # 数据格式
            elif first_lang in ['json']:
                ext = 'json'
            elif first_lang in ['xml']:
                ext = 'xml'
            elif first_lang in ['yaml', 'yml']:
                ext = 'yml'
            elif first_lang in ['toml']:
                ext = 'toml'
            elif first_lang in ['csv']:
                ext = 'csv'

            # 数据库
            elif first_lang in ['sql', 'mysql', 'postgresql', 'sqlite']:
                ext = 'sql'

            # 脚本语言
            elif first_lang in ['bash', 'sh', 'shell']:
                ext = 'sh'
            elif first_lang in ['powershell', 'ps1']:
                ext = 'ps1'
            elif first_lang in ['batch', 'bat', 'cmd']:
                ext = 'bat'

            # 配置文件
            elif first_lang in ['dockerfile', 'docker']:
                ext = 'dockerfile'
            elif first_lang in ['makefile', 'make']:
                ext = 'makefile'
            elif first_lang in ['ini']:
                ext = 'ini'
            elif first_lang in ['conf', 'config']:
                ext = 'conf'
            elif first_lang in ['properties']:
                ext = 'properties'

            # 标记语言
            elif first_lang in ['markdown', 'md']:
                ext = 'md'
            elif first_lang in ['latex', 'tex']:
                ext = 'tex'
            elif first_lang in ['rst']:
                ext = 'rst'

            # 其他
            elif first_lang in ['regex', 'regexp']:
                ext = 'txt'
            elif first_lang in ['log']:
                ext = 'log'
            else:
                ext = 'txt'  # 默认使用txt
        else:
            ext = 'txt'  # 没有代码块时默认使用txt

        filename = f"file_{timestamp}.{ext}"
        file_path = os.path.join(temp_dir, filename)

        # 写入代码内容
        with open(file_path, 'w', encoding='utf-8') as f:
            if len(code_blocks) == 1:
                # 单个代码块：直接写入纯代码，不添加任何元数据
                f.write(code_blocks[0]['code'])
            else:
                # 多个代码块：使用分割符合并，保持简洁
                for i, block in enumerate(code_blocks):
                    if i > 0:
                        f.write("\n" + "=" * 50 + "\n\n")
                    f.write(block['code'])
                    if not block['code'].endswith('\n'):
                        f.write('\n')

        if config.get('AI_AUTO_REPLY', {}).get('debug', False):
            logger.debug(f"代码文件已创建: {file_path}", "代码处理")
        return file_path

    except Exception as e:
        logger.error(f"创建代码文件失败: {e}", "代码处理")
        return None

def extract_markdown_images(message):
    """提取Markdown格式的图片链接"""
    try:
        # 匹配 ![alt](url) 格式的图片
        image_pattern = r'!\[.*?\]\((https?://[^\s\)]+\.(?:jpg|jpeg|png|gif|bmp|webp|svg)(?:\?[^\s\)]*)?)\)'
        matches = re.findall(image_pattern, message, re.IGNORECASE)

        # 去重
        unique_urls = list(set(matches))

        if unique_urls and config.get('AI_AUTO_REPLY', {}).get('debug', False):
            logger.debug(f"提取到 {len(unique_urls)} 个图片链接", "图片处理")

        return unique_urls
    except Exception as e:
        logger.error(f"提取图片链接失败: {e}", "图片处理")
        return []

def download_and_save_image(image_url, user_name):
    """下载并保存图片"""
    try:
        # 创建临时目录
        temp_dir = os.path.join("temp", "images")
        os.makedirs(temp_dir, exist_ok=True)

        # 解析URL获取文件扩展名
        parsed_url = urllib.parse.urlparse(image_url)
        path = parsed_url.path
        ext = os.path.splitext(path)[1]
        if not ext:
            ext = '.jpg'  # 默认扩展名

        # 生成唯一文件名（包含微秒和随机数确保唯一性）
        import random
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        random_suffix = random.randint(1000, 9999)
        filename = f"ai_image_{user_name}_{timestamp}_{random_suffix}{ext}"
        file_path = os.path.join(temp_dir, filename)

        # 如果文件已存在，添加额外的随机后缀
        counter = 1
        original_file_path = file_path
        while os.path.exists(file_path):
            name_without_ext = os.path.splitext(original_file_path)[0]
            file_path = f"{name_without_ext}_{counter}{ext}"
            counter += 1

        # 下载图片
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        req = urllib.request.Request(image_url, headers=headers)
        with urllib.request.urlopen(req, timeout=30) as response:
            if response.status == 200:
                with open(file_path, 'wb') as f:
                    f.write(response.read())

                if config.get('AI_AUTO_REPLY', {}).get('debug', False):
                    logger.debug(f"图片已下载: {file_path}", "图片处理")
                return file_path
            else:
                logger.error(f"下载图片失败，状态码: {response.status}", "图片处理")
                return None

    except Exception as e:
        logger.error(f"下载图片失败 {image_url}: {e}", "图片处理")
        return None

def clean_message_content(message):
    """清理消息内容，移除代码块和图片标记"""
    try:
        # 移除代码块
        cleaned = re.sub(r'```\w*\n.*?\n```', '', message, flags=re.DOTALL)

        # 移除Markdown图片
        cleaned = re.sub(r'!\[.*?\]\(https?://[^\s\)]+\.(?:jpg|jpeg|png|gif|bmp|webp|svg)(?:\?[^\s\)]*)?\)', '', cleaned, flags=re.IGNORECASE)

        # 清理多余的空行
        cleaned = re.sub(r'\n\s*\n', '\n\n', cleaned)

        # 去除首尾空白
        cleaned = cleaned.strip()

        return cleaned
    except Exception as e:
        logger.error(f"清理消息内容失败: {e}", "消息处理")
        return message

def advanced_split_message(message, config):
    """高级智能分段系统 - 支持优先级分段和数组配置"""
    try:
        if not message or message.strip() == '':
            return ['']

        text = message.strip()
        min_length = config.get('min_length', 50)
        max_segments = config.get('max_segments', 5)

        # 如果消息很短，直接返回
        if len(text) <= min_length:
            return [text]

        # 获取分段符号配置（数组形式）
        separators_config = config.get('separators', {})

        # 构建优先级分段符号组
        separator_groups = [
            # 1. 用户自定义分段符号（最高优先级）
            separators_config.get('custom', []),
            # 2. 句末标点（高优先级）
            separators_config.get('sentence_endings', ['。', '！', '？', '.', '!', '?']),
            # 3. 分句标点（中优先级）
            separators_config.get('clause_separators', ['；', ';', '：', ':']),
            # 4. 逗号和顿号（低优先级）
            separators_config.get('comma_separators', ['，', ',', '、']),
            # 5. 换行符（最低优先级）
            separators_config.get('line_separators', ['\\n\\n', '\\n'])
        ]

        # 兼容旧格式的自定义分隔符
        if isinstance(separator_groups[0], str):
            separator_groups[0] = [sep.strip() for sep in separator_groups[0].split(',') if sep.strip()]

        # 处理换行符的转义字符
        line_separators = separator_groups[4]  # 换行符组
        processed_line_separators = []
        for sep in line_separators:
            if sep == '\\n\\n':
                processed_line_separators.append('\n\n')
            elif sep == '\\n':
                processed_line_separators.append('\n')
            else:
                processed_line_separators.append(sep)
        separator_groups[4] = processed_line_separators

        # 逐级优先级分段：先用高优先级分段，再用低优先级细分
        segments = [text]  # 初始为整个文本

        for group_idx, group in enumerate(separator_groups):
            if not group:  # 跳过空组
                continue

            new_segments = []
            for segment in segments:
                # 对每个现有段落尝试用当前优先级的分隔符进一步分段
                best_split = None
                best_count = 1
                best_separator = None

                for separator in group:
                    if separator in segment:
                        # 为每个段落单独计算合理的分段数
                        segment_max = calculate_segment_quota(segment, segments, max_segments)
                        temp_segments = split_by_separator_optimized(segment, separator, segment_max, config)

                        # 选择分段效果最好的分隔符
                        if len(temp_segments) > best_count:
                            # 对于换行符，优先选择能分段更多的
                            if group_idx == 4:  # 换行符组
                                best_split = temp_segments
                                best_count = len(temp_segments)
                                best_separator = separator
                            # 对于其他分隔符，选择合适数量的分段
                            elif len(temp_segments) <= segment_max:
                                best_split = temp_segments
                                best_count = len(temp_segments)
                                best_separator = separator

                # 使用最佳分段结果，如果没有找到合适的分段则保持原样
                if best_split:
                    new_segments.extend(best_split)
                else:
                    new_segments.append(segment)

            segments = new_segments

            # 检查是否需要重新平衡分段
            if len(segments) > max_segments:
                segments = redistribute_segments_evenly(segments, max_segments)
                break  # 已经达到最大分段数，停止进一步分段

            # 检查是否需要重新平衡分段
            if len(segments) > max_segments:
                segments = redistribute_segments_evenly(segments, max_segments)
                break  # 已经达到最大分段数，停止进一步分段

        # 如果仍然只有一段且很长，使用智能长度分段
        if len(segments) == 1 and len(segments[0]) > min_length * 2:
            segments = intelligent_length_split(segments[0], config)

        # 后处理：过滤空段落并限制分段数量
        segments = [seg.strip() for seg in segments if seg.strip()]

        # 最终检查：确保不超过最大分段数
        if len(segments) > max_segments:
            segments = redistribute_segments_evenly(segments, max_segments)

        return segments if segments else [message]

    except Exception as e:
        logger.error(f"高级分段失败: {e}", "消息处理")
        return [message]

def calculate_segment_quota(current_segment, all_segments, max_segments):
    """计算当前段落应该分成多少段"""
    # 基于当前段落在总文本中的比例来分配分段配额
    total_length = sum(len(seg) for seg in all_segments)
    current_length = len(current_segment)

    if total_length == 0:
        return max_segments

    # 计算比例配额，至少为2（如果要分段的话）
    proportion = current_length / total_length
    quota = max(2, int(max_segments * proportion))

    # 限制最大配额不超过总配额
    return min(quota, max_segments)

def split_by_separator_optimized(text, separator, max_segments, config):
    """优化的分隔符分段 - 支持可配置标点删除和智能平均分配"""
    parts = text.split(separator)
    if len(parts) <= 1:
        return [text]

    segments = []
    target_length = config.get('target_length', 100)
    min_segment_length = config.get('min_segment_length', 1)

    # 从配置中获取是否删除标点符号的设置
    remove_punctuation = config.get('separators', {}).get('remove_punctuation', True)

    # 定义需要删除的标点符号（当启用删除功能时）
    punctuation_to_remove = ['。', '！', '？', '.', '!', '?', '；', ';', '：', ':', '，', ',', '、']

    # 简化逻辑：直接按分隔符分段，然后决定是否保留分隔符
    for i, part in enumerate(parts):
        if part.strip():  # 只处理非空部分
            if i == len(parts) - 1:
                # 最后一部分，直接添加
                segments.append(part.strip())
            else:
                # 非最后部分，检查是否需要保留分隔符
                should_remove_separator = False

                if remove_punctuation and separator in punctuation_to_remove:
                    # 启用标点删除且是标点符号：删除分隔符
                    should_remove_separator = True
                elif separator in ['\n\n', '\n']:
                    # 换行符：总是删除
                    should_remove_separator = True

                if should_remove_separator:
                    segments.append(part.strip())
                else:
                    # 保留分隔符
                    segments.append((part + separator).strip())

    # 智能处理分段过多的情况 - 平均分配
    if len(segments) > max_segments:
        segments = redistribute_segments_evenly(segments, max_segments)

    return segments if segments else [text]

def redistribute_segments_evenly(segments, max_segments):
    """智能重新分配段落 - 尽可能平均分配"""
    if len(segments) <= max_segments:
        return segments

    # 计算每段应该包含的原始段落数
    total_segments = len(segments)
    segments_per_group = total_segments / max_segments

    result = []
    current_group = []
    current_count = 0
    target_count = segments_per_group

    for i, segment in enumerate(segments):
        current_group.append(segment)
        current_count += 1

        # 检查是否应该结束当前组
        should_end_group = False

        if i == len(segments) - 1:
            # 最后一个段落，必须结束
            should_end_group = True
        elif len(result) >= max_segments - 1:
            # 已经达到最大组数-1，剩余全部合并
            should_end_group = False
        elif current_count >= target_count:
            # 达到目标数量
            should_end_group = True
            target_count += segments_per_group  # 更新下一组的目标

        if should_end_group:
            # 合并当前组的所有段落
            merged_segment = ' '.join(current_group)
            result.append(merged_segment)
            current_group = []
            current_count = 0

    # 处理剩余段落（如果有）
    if current_group:
        if result:
            # 合并到最后一组
            result[-1] += ' ' + ' '.join(current_group)
        else:
            # 作为新组
            result.append(' '.join(current_group))

    return result

def split_by_separator(text, separator, max_segments, config):
    """按指定分隔符分段 - 保持向后兼容"""
    return split_by_separator_optimized(text, separator, max_segments, config)

def intelligent_length_split(text, config):
    """智能长度分段"""
    max_segments = config.get('max_segments', 5)
    target_length = config.get('target_length', 100)
    min_segment_length = config.get('min_segment_length', 30)

    if len(text) <= target_length:
        return [text]

    segments = []
    remaining = text

    while remaining and len(segments) < max_segments:
        if len(remaining) <= target_length or len(segments) == max_segments - 1:
            # 最后一段或剩余内容不长
            segments.append(remaining)
            break

        # 寻找合适的分割点
        split_point = find_best_split_point(remaining, target_length, min_segment_length)

        if split_point > 0:
            segments.append(remaining[:split_point].strip())
            remaining = remaining[split_point:].strip()
        else:
            # 找不到合适分割点，强制分割
            segments.append(remaining[:target_length])
            remaining = remaining[target_length:]

    return segments

def find_best_split_point(text, target_length, min_length):
    """寻找最佳分割点"""
    if len(text) <= target_length:
        return len(text)

    # 在目标长度附近寻找合适的分割点
    search_range = min(50, target_length // 3)
    start_pos = max(min_length, target_length - search_range)
    end_pos = min(len(text), target_length + search_range)

    # 优先寻找标点符号
    punctuation = ['。', '！', '？', '.', '!', '?', '；', ';', '，', ',', '、', '\n']

    for i in range(end_pos - 1, start_pos - 1, -1):
        if text[i] in punctuation:
            return i + 1

    # 如果找不到标点，寻找空格
    for i in range(end_pos - 1, start_pos - 1, -1):
        if text[i] == ' ':
            return i + 1

    # 最后使用目标长度
    return target_length

def merge_excess_segments(segments, max_segments):
    """智能合并多余的段落"""
    if len(segments) <= max_segments:
        return segments

    # 保留前面的段落，合并后面的
    result = segments[:max_segments-1]
    remaining = ' '.join(segments[max_segments-1:])
    result.append(remaining)

    return result

def smart_split_message(message, max_segments=5, punctuation_priority=True, custom_separators=''):
    """智能分割消息 - 支持自定义分段符号和优先级分段"""
    try:
        if not message or message.strip() == '':
            return ['']

        # 如果消息很短，直接返回
        if len(message) <= 50:
            return [message]

        segments = []
        text = message.strip()

        if punctuation_priority:
            # 构建分段符号列表，优先级从高到低
            separators = []

            # 用户自定义分段符号（最高优先级）
            if custom_separators:
                custom_list = [sep.strip() for sep in custom_separators.split(',') if sep.strip()]
                separators.extend(custom_list)

            # 默认分段符号（按优先级）
            default_separators = [
                '。', '！', '？', '.', '!', '?',  # 句末标点（最高优先级）
                '；', ';',                        # 分号
                '，', ',',                        # 逗号
                '：', ':',                        # 冒号
                '、',                             # 顿号
                '\n\n',                          # 双换行
                '\n'                             # 单换行
            ]
            separators.extend(default_separators)

            # 按优先级尝试分段
            for separator in separators:
                if segments:
                    break

                if separator in text:
                    parts = text.split(separator)
                    if len(parts) > 1:
                        temp_segments = []
                        current_seg = ''

                        for i, part in enumerate(parts):
                            if i == len(parts) - 1:
                                # 最后一部分
                                current_seg += part
                            else:
                                # 非最后部分，加上分隔符
                                current_seg += part + separator

                            # 检查是否应该结束当前段落
                            if (len(temp_segments) < max_segments - 1 and
                                len(current_seg) > 30 and
                                i < len(parts) - 1):  # 不是最后一个部分
                                temp_segments.append(current_seg.strip())
                                current_seg = ''

                        # 处理剩余内容
                        if current_seg.strip():
                            temp_segments.append(current_seg.strip())

                        # 如果分段数量合适，使用这个结果
                        if 1 < len(temp_segments) <= max_segments:
                            segments = temp_segments
                            break

        # 如果按标点符号分段失败，使用长度分段
        if not segments:
            segments = fallback_length_split(text, max_segments)

        # 过滤空段落并限制分段数量
        segments = [seg.strip() for seg in segments if seg.strip()]

        if len(segments) > max_segments:
            # 合并多余的段落到最后一段
            result = segments[:max_segments-1]
            remaining = ' '.join(segments[max_segments-1:])
            result.append(remaining)
            segments = result

        return segments if segments else [message]

    except Exception as e:
        logger.error(f"智能分割消息失败: {e}", "消息处理")
        return [message]

def fallback_length_split(text, max_segments):
    """后备的长度分段方法"""
    try:
        if len(text) <= 100:
            return [text]

        # 计算平均分段长度
        avg_length = len(text) // min(max_segments, 3)
        min_length = 50
        max_length = avg_length * 2

        segments = []
        remaining = text

        while remaining and len(segments) < max_segments:
            if len(remaining) <= max_length or len(segments) == max_segments - 1:
                # 最后一段或剩余内容不长
                segments.append(remaining)
                break

            # 寻找合适的分割点
            cut_point = avg_length
            best_cut = cut_point

            # 在目标长度附近寻找合适的分割点
            search_range = min(50, avg_length // 2)
            for i in range(max(min_length, cut_point - search_range),
                          min(len(remaining), cut_point + search_range)):
                char = remaining[i]
                if char in ' \n\t，。！？；：':
                    best_cut = i + 1
                    break

            segments.append(remaining[:best_cut].strip())
            remaining = remaining[best_cut:].strip()

        return segments if segments else [text]

    except Exception as e:
        logger.error(f"后备分段方法失败: {e}", "消息处理")
        return [text]

def split_message_into_segments(message, max_length):
    """将长消息分割成多个段落"""
    if len(message) <= max_length:
        return [message]

    segments = []
    # 优先按句号分割
    sentences = message.split('。')
    current_segment = ""

    for sentence in sentences:
        if sentence.strip():
            test_segment = current_segment + sentence + '。' if current_segment else sentence + '。'
            if len(test_segment) <= max_length:
                current_segment = test_segment
            else:
                if current_segment:
                    segments.append(current_segment.strip())
                    current_segment = sentence + '。'
                else:
                    # 单个句子太长，按字符强制分割
                    while len(sentence) > max_length:
                        segments.append(sentence[:max_length])
                        sentence = sentence[max_length:]
                    if sentence:
                        current_segment = sentence + '。'

    if current_segment:
        segments.append(current_segment.strip())

    return segments

# 消息监听器
def message_listener(msg, chat):
    """消息监听器 - 记录所有消息"""
    import os  # 确保 os 模块在整个函数作用域内可用
    try:
        # 过滤掉系统消息
        if msg.type == 'base' or msg.sender == 'system':
            logger.debug(f"忽略系统消息: {msg.type} - {msg.content}")
            return

        # 获取消息信息
        user_name = chat.who
        sender_name = msg.sender

        # 修复sender_name显示问题
        if sender_name == "self":
            # 机器人发送的消息，使用机器人名称
            sender_name = ROBOT_WX_NAME or "机器人"
            # 过滤掉AI回复的WebSocket事件，避免重复输出
            logger.debug(f"忽略AI回复消息的WebSocket事件: {msg.content[:50]}", "消息监听")
            return

        # 检查是否在监听列表中
        listen_list = config.get('LISTEN_LIST', [])
        target_listener = None

        for listener in listen_list:
            if listener.get('name') == user_name:
                target_listener = listener
                break

        if not target_listener:
            logger.debug(f"用户 {user_name} 不在监听列表中，已忽略")
            return

        user_type = target_listener.get('type', 'private')

        # 处理不同类型的消息并构建详细内容
        detailed_content = msg.content  # 默认值

        if msg.type == 'text':
            # 文本消息：根据聊天类型格式化
            if user_type == 'private':
                detailed_content = msg.content
            else:
                detailed_content = f"{sender_name}说: {msg.content}"
            save_message_to_file(user_name, user_type, sender_name, detailed_content, "text")

        elif msg.type == 'voice':
            try:
                # 获取语音基本信息
                voice_info = msg.content
                logger.debug(f"语音消息基本信息: {voice_info}")

                # 尝试获取语音转文本
                voicetext = None
                if hasattr(msg, 'to_text'):
                    try:
                        voicetext = msg.to_text()
                        logger.debug(f"语音转文字结果: {voicetext}")
                    except Exception as e:
                        logger.debug(f"语音转文字失败: {e}")

                # 尝试下载语音文件
                voice_path = None
                if hasattr(msg, 'download'):
                    try:
                        voice_path = msg.download()
                        logger.file(f"语音文件下载成功: {voice_path}", "文件处理")
                    except Exception as e:
                        logger.warning(f"语音文件下载失败: {e}", "文件处理")

                # 构建详细内容
                if voice_path:
                    import os
                    filename = os.path.basename(voice_path)
                    if user_type == 'private':
                        detailed_content = f"发送了语音![{filename}]({voice_path})"
                    else:
                        detailed_content = f"{sender_name}发送了语音![{filename}]({voice_path})"
                else:
                    if user_type == 'private':
                        detailed_content = "发送了语音"
                    else:
                        detailed_content = f"{sender_name}发送了语音"

                if voicetext and voicetext.strip():
                    detailed_content += f" [语音转文字]: {voicetext}"

                save_message_to_file(user_name, user_type, sender_name, detailed_content, "voice", voice_path)
                logger.success(f"语音消息处理完成: 转文字={bool(voicetext)}, 文件下载={bool(voice_path)}", "消息处理")

            except Exception as e:
                logger.error(f"处理语音消息失败: {e}", "消息处理")
                save_message_to_file(user_name, user_type, sender_name, msg.content, "voice")

        elif msg.type == 'image':
            try:
                # 获取图片基本信息
                image_info = msg.content  # 如 "[图片]"
                logger.debug(f"图片消息基本信息: {image_info}")

                # 尝试下载图片文件
                image_path = None
                if hasattr(msg, 'download'):
                    try:
                        image_path = msg.download()
                        logger.file(f"图片文件下载成功: {image_path}", "文件处理")
                    except Exception as e:
                        logger.warning(f"图片文件下载失败: {e}", "文件处理")

                # 构建详细内容
                if image_path:
                    filename = os.path.basename(image_path)
                    if user_type == 'private':
                        detailed_content = f"发送了图片![{filename}]({image_path})"
                    else:
                        detailed_content = f"{sender_name}发送了图片![{filename}]({image_path})"
                else:
                    if user_type == 'private':
                        detailed_content = "发送了图片"
                    else:
                        detailed_content = f"{sender_name}发送了图片"

                save_message_to_file(user_name, user_type, sender_name, detailed_content, "image", image_path)

                logger.success(f"图片消息处理完成: 文件下载={bool(image_path)}", "消息处理")

            except Exception as e:
                logger.error(f"处理图片消息失败: {e}", "消息处理")
                logger.debug(f"图片处理错误详情: {traceback.format_exc()}", "消息处理")
                save_message_to_file(user_name, user_type, sender_name, msg.content, "image")

        elif msg.type == 'file':
            try:
                # 获取文件基本信息
                file_info = msg.content  # 如 "[文件]"
                logger.debug(f"文件消息基本信息: {file_info}")

                # 尝试下载文件
                file_path = None
                if hasattr(msg, 'download'):
                    try:
                        file_path = msg.download()
                        logger.file(f"文件下载成功: {file_path}", "文件处理")
                    except Exception as e:
                        logger.warning(f"文件下载失败: {e}", "文件处理")

                # 构建详细内容
                if file_path:
                    filename = os.path.basename(file_path)
                    if user_type == 'private':
                        detailed_content = f"发送了文件![{filename}]({file_path})"
                    else:
                        detailed_content = f"{sender_name}发送了文件![{filename}]({file_path})"
                else:
                    if user_type == 'private':
                        detailed_content = "发送了文件"
                    else:
                        detailed_content = f"{sender_name}发送了文件"

                save_message_to_file(user_name, user_type, sender_name, detailed_content, "file", file_path)

                logger.success(f"文件消息处理完成: 文件下载={bool(file_path)}", "消息处理")

            except Exception as e:
                logger.error(f"处理文件消息失败: {e}", "消息处理")
                logger.debug(f"文件处理错误详情: {traceback.format_exc()}", "消息处理")
                save_message_to_file(user_name, user_type, sender_name, msg.content, "file")

        elif msg.type == 'link':
            try:
                # 尝试获取链接URL
                link_url = None
                if hasattr(msg, 'get_url'):
                    try:
                        link_url = msg.get_url()
                    except Exception as e:
                        logger.debug(f"获取链接URL失败: {e}")

                # 构建详细内容
                if link_url:
                    if user_type == 'private':
                        detailed_content = f"发送了链接: {msg.content} - {link_url}"
                    else:
                        detailed_content = f"{sender_name}发送了链接: {msg.content} - {link_url}"
                else:
                    if user_type == 'private':
                        detailed_content = f"发送了链接: {msg.content}"
                    else:
                        detailed_content = f"{sender_name}发送了链接: {msg.content}"

                save_message_to_file(user_name, user_type, sender_name, detailed_content, "link")
            except Exception as e:
                logger.error(f"处理链接消息失败: {e}", "消息处理")
                save_message_to_file(user_name, user_type, sender_name, msg.content, "link")

        elif msg.type == 'quote':
            try:
                # 处理引用消息
                current_message = msg.content

                # 获取被引用的消息内容
                if hasattr(msg, 'quote_content') and msg.quote_content:
                    quoted_message = msg.quote_content

                    # 根据聊天类型格式化引用消息
                    if user_type == 'private':
                        if quoted_message == "[图片]":
                            # 只有引用图片时才下载图片
                            if hasattr(msg, 'download_quote_image'):
                                try:
                                    result = msg.download_quote_image()
                                    if result:
                                        import os
                                        from pathlib import Path
                                        if isinstance(result, Path):
                                            result_path = str(result)
                                        else:
                                            result_path = result

                                        if os.path.exists(result_path):
                                            filename = os.path.basename(result_path)
                                            detailed_content = f"引用了图片![{filename}]({result_path})，然后说「{current_message}」"
                                        else:
                                            detailed_content = f"引用了图片，然后说「{current_message}」"
                                    else:
                                        detailed_content = f"引用了图片，然后说「{current_message}」"
                                except Exception as e:
                                    detailed_content = f"引用了图片，然后说「{current_message}」"
                            else:
                                detailed_content = f"引用了图片，然后说「{current_message}」"
                        elif quoted_message and quoted_message not in ['[图片]', '[语音]']:
                            # 先检查是否是文件引用
                            import os
                            file_download_dir = os.path.join(os.path.dirname(__file__), "wxautox文件下载")
                            file_path = os.path.join(file_download_dir, quoted_message)
                            if os.path.exists(file_path):
                                # 确实是文件引用
                                detailed_content = f"引用了文件![{quoted_message}]({file_path})，然后说「{current_message}」"
                            else:
                                # 不是文件，是普通文本引用
                                detailed_content = f"引用了消息「{quoted_message}」，然后说「{current_message}」"
                        else:
                            detailed_content = f"引用了消息「{quoted_message}」，然后说「{current_message}」"
                    else:
                        # 群聊模式：显示群聊消息
                        if quoted_message == "[图片]":
                            # 只有引用图片时才下载图片
                            if hasattr(msg, 'download_quote_image'):
                                try:
                                    result = msg.download_quote_image()
                                    if result:
                                        import os
                                        from pathlib import Path
                                        if isinstance(result, Path):
                                            result_path = str(result)
                                        else:
                                            result_path = result

                                        if os.path.exists(result_path):
                                            filename = os.path.basename(result_path)
                                            detailed_content = f"{sender_name}引用了群聊中的图片![{filename}]({result_path})，然后说「{current_message}」"
                                        else:
                                            detailed_content = f"{sender_name}引用了群聊中的图片，然后说「{current_message}」"
                                    else:
                                        detailed_content = f"{sender_name}引用了群聊中的图片，然后说「{current_message}」"
                                except Exception as e:
                                    detailed_content = f"{sender_name}引用了群聊中的图片，然后说「{current_message}」"
                            else:
                                detailed_content = f"{sender_name}引用了群聊中的图片，然后说「{current_message}」"
                        elif quoted_message and quoted_message not in ['[图片]', '[语音]']:
                            # 先检查是否是文件引用
                            import os
                            file_download_dir = os.path.join(os.path.dirname(__file__), "wxautox文件下载")
                            file_path = os.path.join(file_download_dir, quoted_message)
                            if os.path.exists(file_path):
                                # 确实是文件引用
                                detailed_content = f"{sender_name}引用了群聊中的文件![{quoted_message}]({file_path})，然后说「{current_message}」"
                            else:
                                # 不是文件，是普通文本引用
                                detailed_content = f"{sender_name}引用了群聊消息「{quoted_message}」，然后说「{current_message}」"
                        else:
                            detailed_content = f"{sender_name}引用了群聊消息「{quoted_message}」，然后说「{current_message}」"

                else:
                    # 如果没有quote_content属性，使用原始内容
                    if user_type == 'private':
                        detailed_content = f"发送了引用消息: {current_message}"
                    else:
                        detailed_content = f"{sender_name}发送了引用消息: {current_message}"

                save_message_to_file(user_name, user_type, sender_name, detailed_content, "quote")

            except Exception as e:
                logger.error(f"处理引用消息失败: {e}", "引用调试")
                if user_type == 'private':
                    detailed_content = f"发送了引用消息: {msg.content}"
                else:
                    detailed_content = f"{sender_name}发送了引用消息: {msg.content}"
                save_message_to_file(user_name, user_type, sender_name, detailed_content, "quote")

        else:
            # 其他类型的消息
            if user_type == 'private':
                detailed_content = f"发送了{msg.type}消息: {msg.content}"
            else:
                detailed_content = f"{sender_name}发送了{msg.type}消息: {msg.content}"
            save_message_to_file(user_name, user_type, sender_name, detailed_content, msg.type)

        # 更新监听状态
        current_time = datetime.now()
        with listener_lock:
            if user_name not in listener_status:
                listener_status[user_name] = {
                    'user_name': user_name,
                    'user_type': user_type,
                    'is_active': True,
                    'last_message_time': current_time.isoformat(),
                    'message_count': 0,
                    'listener_added_time': current_time.isoformat(),
                    'status': 'active'
                }

            listener_status[user_name]['is_active'] = True
            listener_status[user_name]['last_message_time'] = current_time.isoformat()
            listener_status[user_name]['message_count'] += 1
            listener_status[user_name]['status'] = 'active'

        # 只在debug模式下输出详细消息记录
        if config.get('AI_AUTO_REPLY', {}).get('debug', False):
            logger.debug(f"消息: {user_name} - {sender_name} - {msg.type}", "消息监听")

        # detailed_content 已经在上面的消息处理逻辑中构建完成

        # 发送结构化事件数据
        # 确保所有路径都转换为字符串
        safe_file_path = None
        if 'file_path' in locals() and file_path:
            safe_file_path = str(file_path)
        elif 'image_path' in locals() and image_path:
            safe_file_path = str(image_path)
        elif 'voice_path' in locals() and voice_path:
            safe_file_path = str(voice_path)

        event_data = {
            "event_type": "message",
            "message_type": user_type,  # private 或 group
            "user_name": user_name,
            "sender_name": sender_name,
            "content": detailed_content,  # 使用详细的内容格式
            "raw_content": msg.content,   # 保留原始内容
            "content_type": msg.type,  # text, voice, image, file, link等
            "timestamp": datetime.now().isoformat(),
            "file_path": safe_file_path,
            "voice_text": voicetext if 'voicetext' in locals() else None
        }
        send_event_to_webhook(event_data)

        # AI自动回复逻辑
        try:
            # 只在有AI回复时才输出日志
            handle_ai_auto_reply(event_data)
        except Exception as ai_error:
            logger.error(f"AI自动回复处理失败: {ai_error}", "AI系统")
            import traceback
            logger.debug(f"AI错误详情: {traceback.format_exc()}", "AI系统")

    except Exception as e:
        logger.error(f"消息监听器发生异常: {e}", "消息监听")
        logger.debug(f"监听器异常详情: {traceback.format_exc()}", "消息监听")

class AdvancedListenerManager:
    """高级监听管理器 - 基于事件驱动的实时监听系统"""

    def __init__(self):
        self.listener_events = {}  # 存储每个监听器的事件对象
        self.listener_threads = {}  # 存储监听线程
        self.health_check_interval = 60  # 健康检查间隔（秒）
        self.reconnect_attempts = 3  # 重连尝试次数
        self.is_running = False

    def start_health_monitor(self):
        """启动健康监控线程"""
        if self.is_running:
            return

        self.is_running = True
        health_thread = threading.Thread(target=self._health_monitor_loop, daemon=True)
        health_thread.start()
        logger.system("高级监听健康监控已启动", "监听管理器")

    def stop_health_monitor(self):
        """停止健康监控"""
        self.is_running = False
        logger.system("高级监听健康监控已停止", "监听管理器")

    def _health_monitor_loop(self):
        """健康监控循环 - 使用智能检测而非固定轮询"""
        logger.listener("健康监控线程启动", "监听管理器")

        while self.is_running and bot_running:
            try:
                # 检查微信连接状态
                if not self._check_wechat_connection():
                    logger.warning("微信连接异常，尝试重新连接", "监听管理器")
                    self._attempt_reconnect()
                    continue

                # 检查监听器状态
                self._check_listeners_health()

                # 智能等待 - 根据系统负载调整检查间隔
                adaptive_interval = self._calculate_adaptive_interval()
                time.sleep(adaptive_interval)

            except Exception as e:
                logger.error(f"健康监控异常: {e}", "监听管理器")
                time.sleep(30)  # 异常时等待30秒

    def _check_wechat_connection(self):
        """检查微信连接状态"""
        try:
            if not wx:
                return False

            # 尝试获取微信昵称来验证连接
            if hasattr(wx, 'nickname') and wx.nickname:
                return True

            return False
        except Exception:
            return False

    def _attempt_reconnect(self):
        """尝试重新连接微信"""
        for attempt in range(self.reconnect_attempts):
            try:
                logger.info(f"尝试重新连接微信 (第{attempt + 1}次)", "监听管理器")

                if init_wechat():
                    logger.success("微信重新连接成功", "监听管理器")
                    # 重新添加所有监听器
                    self._restore_all_listeners()
                    return True

            except Exception as e:
                logger.error(f"重连尝试失败: {e}", "监听管理器")
                time.sleep(5 * (attempt + 1))  # 递增等待时间

        logger.error("微信重连失败，已达到最大尝试次数", "监听管理器")
        return False

    def _check_listeners_health(self):
        """检查监听器健康状态"""
        if not wx or not hasattr(wx, 'listen'):
            return

        try:
            current_listeners = set(wx.listen.keys()) if wx.listen else set()
            expected_listeners = set([listener['name'] for listener in config.get("LISTEN_LIST", [])])

            # 检查丢失的监听器
            missing_listeners = expected_listeners - current_listeners
            if missing_listeners:
                logger.warning(f"检测到 {len(missing_listeners)} 个监听器丢失: {', '.join(missing_listeners)}", "监听管理器")
                self._restore_missing_listeners(missing_listeners)

            # 检查多余的监听器
            extra_listeners = current_listeners - expected_listeners
            if extra_listeners:
                logger.info(f"检测到 {len(extra_listeners)} 个多余监听器: {', '.join(extra_listeners)}", "监听管理器")
                self._remove_extra_listeners(extra_listeners)

        except Exception as e:
            logger.error(f"监听器健康检查失败: {e}", "监听管理器")

    def _restore_missing_listeners(self, missing_listeners):
        """恢复丢失的监听器"""
        for user_name in missing_listeners:
            try:
                logger.info(f"正在恢复监听器: {user_name}", "监听管理器")
                wx.AddListenChat(nickname=user_name, callback=message_listener)

                with listener_lock:
                    if user_name in listener_status:
                        listener_status[user_name]['status'] = 'listening'
                        listener_status[user_name]['last_restore_time'] = datetime.now().isoformat()

                logger.success(f"监听器恢复成功: {user_name}", "监听管理器")

            except Exception as e:
                logger.error(f"恢复监听器失败 {user_name}: {e}", "监听管理器")
                with listener_lock:
                    if user_name in listener_status:
                        listener_status[user_name]['status'] = 'error'

    def _remove_extra_listeners(self, extra_listeners):
        """移除多余的监听器"""
        for user_name in extra_listeners:
            try:
                if hasattr(wx, 'RemoveListenChat'):
                    wx.RemoveListenChat(nickname=user_name)
                    logger.info(f"已移除多余监听器: {user_name}", "监听管理器")
            except Exception as e:
                logger.warning(f"移除多余监听器失败 {user_name}: {e}", "监听管理器")

    def _restore_all_listeners(self):
        """恢复所有监听器"""
        for listener in config.get("LISTEN_LIST", []):
            user_name = listener['name']
            try:
                wx.AddListenChat(nickname=user_name, callback=message_listener)
                logger.success(f"监听器恢复: {user_name}", "监听管理器")
            except Exception as e:
                logger.error(f"监听器恢复失败 {user_name}: {e}", "监听管理器")

    def _calculate_adaptive_interval(self):
        """计算自适应检查间隔"""
        # 基础间隔
        base_interval = self.health_check_interval

        # 根据监听器数量调整
        listener_count = len(config.get("LISTEN_LIST", []))
        if listener_count > 10:
            base_interval = max(30, base_interval - 10)  # 监听器多时更频繁检查
        elif listener_count < 3:
            base_interval = min(120, base_interval + 30)  # 监听器少时减少检查频率

        # 根据最近错误率调整
        recent_errors = self._get_recent_error_count()
        if recent_errors > 3:
            base_interval = max(20, base_interval - 20)  # 错误多时更频繁检查

        return base_interval

    def _get_recent_error_count(self):
        """获取最近的错误计数"""
        error_count = 0

        with listener_lock:
            for status in listener_status.values():
                if status.get('status') == 'error':
                    error_count += 1

        return error_count

# 创建全局监听管理器实例
advanced_listener_manager = AdvancedListenerManager()

# 保留原有的简化版本作为备用
def keep_alive():
    """备用监听守护线程 - 简化版本"""
    logger.warning("使用备用监听守护线程", "监听系统")
    check_interval = 45  # 增加间隔减少资源消耗

    while bot_running:
        try:
            if wx and hasattr(wx, 'listen'):
                current_listening_users = set(wx.listen.keys())
                expected_users = set([listener['name'] for listener in config.get("LISTEN_LIST", [])])
                missing_users = expected_users - current_listening_users

                if missing_users:
                    logger.warning(f"检测到 {len(missing_users)} 个用户从监听列表中丢失: {', '.join(missing_users)}", "备用监听")
                    for user in missing_users:
                        try:
                            wx.AddListenChat(nickname=user, callback=message_listener)
                            logger.success(f"重新添加监听用户: {user}", "备用监听")

                            with listener_lock:
                                if user in listener_status:
                                    listener_status[user]['status'] = 'listening'
                        except Exception as e:
                            logger.error(f"重新添加用户失败 {user}: {e}", "备用监听")
                else:
                    logger.debug(f"监听状态正常，共 {len(expected_users)} 个用户", "备用监听")
            else:
                logger.warning("微信对象不可用", "备用监听")

        except Exception as e:
            logger.error(f"备用监听检查异常: {e}", "备用监听")

        time.sleep(check_interval)

# 微信初始化
def init_wechat():
    """初始化微信"""
    global wx, ROBOT_WX_NAME
    try:
        logger.system("开始初始化微信连接", "微信初始化")
        pythoncom.CoInitialize()
        from wxautox_wechatbot import WeChat
        wx = WeChat()
        ROBOT_WX_NAME = wx.nickname
        logger.success(f"微信初始化成功，机器人昵称: {ROBOT_WX_NAME}", "微信初始化")
        return True
    except Exception as e:
        logger.error(f"微信初始化失败: {e}", "微信初始化")
        return False

# 自动启动监听
def auto_start_listening():
    """自动启动监听 - 使用高级监听系统"""
    global bot_running

    if not config.get("AUTO_START_LISTENING", True):
        logger.info("自动启动监听已禁用", "启动系统")
        return

    if not config.get("LISTEN_LIST"):
        logger.info("监听列表为空，跳过自动启动", "启动系统")
        return

    try:
        logger.system("开始启动消息监听系统", "启动系统")
        bot_running = True

        # 优先使用高级监听管理器
        try:
            advanced_listener_manager.start_health_monitor()
            logger.success("高级监听管理器启动成功", "启动系统")
        except Exception as e:
            logger.warning(f"高级监听管理器启动失败，使用备用系统: {e}", "启动系统")
            # 启动备用监听守护线程
            keep_alive_thread = threading.Thread(target=keep_alive, daemon=True)
            keep_alive_thread.start()
            logger.info("备用监听守护线程已启动", "启动系统")

        # 添加监听用户
        success_count = 0
        error_count = 0

        for listener in config.get("LISTEN_LIST", []):
            user_name = listener['name']
            user_type = listener.get('type', 'private')

            try:
                wx.AddListenChat(nickname=user_name, callback=message_listener)
                logger.success(f"监听用户添加成功: {user_name} ({user_type})", "启动系统")
                success_count += 1

                # 初始化监听状态
                current_time = datetime.now()
                with listener_lock:
                    listener_status[user_name] = {
                        'user_name': user_name,
                        'user_type': user_type,
                        'is_active': False,
                        'last_message_time': None,
                        'message_count': 0,
                        'listener_added_time': current_time.isoformat(),
                        'status': 'listening',
                        'last_restore_time': None,
                        'error_count': 0
                    }

            except Exception as e:
                logger.error(f"添加监听用户失败 {user_name}: {e}", "启动系统")
                error_count += 1

                current_time = datetime.now()
                with listener_lock:
                    listener_status[user_name] = {
                        'user_name': user_name,
                        'user_type': user_type,
                        'is_active': False,
                        'last_message_time': None,
                        'message_count': 0,
                        'listener_added_time': current_time.isoformat(),
                        'status': 'error',
                        'last_restore_time': None,
                        'error_count': 1
                    }

        # 启动结果统计
        total_listeners = len(config.get("LISTEN_LIST", []))
        logger.success(f"监听系统启动完成！成功: {success_count}/{total_listeners}, 失败: {error_count}", "启动系统")

        if success_count > 0:
            logger.system("🎧 微信消息监听系统已就绪", "启动系统")

    except Exception as e:
        logger.error(f"自动启动监听失败: {e}", "启动系统")
        bot_running = False

@app.route('/api/status', methods=['GET'])
def api_get_status():
    """获取系统状态"""
    try:
        status = {
            "success": True,
            "data": {
                "bot_running": bot_running,
                "wechat_connected": wx is not None,
                "robot_name": ROBOT_WX_NAME,
                "listeners": [listener['name'] for listener in config.get('LISTEN_LIST', [])],
                "listeners_count": len(config.get('LISTEN_LIST', [])),
                "timestamp": datetime.now().isoformat(),
                "log_directory": config.get("LOG_DIR", "chat_logs"),
                "auto_start_enabled": config.get("AUTO_START_LISTENING", True),
                "features": {
                    "message_logging": True,
                    "real_time_monitoring": True,
                    "file_based_storage": True,
                    "message_sending": True,
                    "file_sending": True,
                    "auto_start_listening": True
                }
            }
        }
        return jsonify(status)
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/bot/start', methods=['POST'])
def api_start_bot():
    """启动Bot监听"""
    global bot_running

    try:
        if bot_running:
            return jsonify({"success": False, "message": "Bot已在运行中"})

        if not wx:
            return jsonify({"success": False, "message": "微信未初始化"})

        logger.info("手动启动消息监听...", "API接口")
        bot_running = True

        # 启动监听守护线程
        keep_alive_thread = threading.Thread(target=keep_alive, daemon=True)
        keep_alive_thread.start()
        logger.info("监听守护线程已启动", "API接口")

        # 添加监听用户
        for listener in config.get("LISTEN_LIST", []):
            user_name = listener['name']
            user_type = listener.get('type', 'private')

            try:
                wx.AddListenChat(nickname=user_name, callback=message_listener)
                logger.info(f"已添加监听用户: {user_name} ({user_type})", "API接口")

                # 初始化监听状态
                current_time = datetime.now()
                with listener_lock:
                    listener_status[user_name] = {
                        'user_name': user_name,
                        'user_type': user_type,
                        'is_active': False,
                        'last_message_time': None,
                        'message_count': 0,
                        'listener_added_time': current_time.isoformat(),
                        'status': 'listening'
                    }

            except Exception as e:
                logger.error(f"添加监听用户 {user_name} 失败: {e}", "API接口")

                current_time = datetime.now()
                with listener_lock:
                    listener_status[user_name] = {
                        'user_name': user_name,
                        'user_type': user_type,
                        'is_active': False,
                        'last_message_time': None,
                        'message_count': 0,
                        'listener_added_time': current_time.isoformat(),
                        'status': 'error'
                    }

        logger.success("🎧 消息监听启动成功！", "API接口")
        return jsonify({"success": True, "message": "消息监听启动成功"})

    except Exception as e:
        logger.error(f"启动Bot失败: {e}", "API接口")
        bot_running = False
        return jsonify({"success": False, "message": f"启动失败: {str(e)}"}), 500

@app.route('/api/bot/stop', methods=['POST'])
def api_stop_bot():
    """停止Bot监听"""
    global bot_running

    try:
        if not bot_running:
            logger.warning("Bot未在运行，无需停止", "API接口")
            return jsonify({"success": False, "message": "Bot未在运行"})

        logger.system("开始停止消息监听系统", "API接口")
        bot_running = False

        # 停止高级监听管理器
        try:
            advanced_listener_manager.stop_health_monitor()
            logger.success("高级监听管理器已停止", "API接口")
        except Exception as e:
            logger.warning(f"停止高级监听管理器失败: {e}", "API接口")

        # 移除所有监听用户
        removed_count = 0
        if wx:
            for listener in config.get("LISTEN_LIST", []):
                user_name = listener['name']
                try:
                    wx.RemoveListenChat(nickname=user_name)
                    logger.success(f"移除监听用户: {user_name}", "API接口")
                    removed_count += 1
                except Exception as e:
                    logger.error(f"移除监听用户失败 {user_name}: {e}", "API接口")

        # 清理监听状态
        with listener_lock:
            status_count = len(listener_status)
            listener_status.clear()
            logger.info(f"已清理 {status_count} 个监听状态", "API接口")

        logger.success(f"🛑 消息监听系统已停止，共移除 {removed_count} 个监听", "API接口")
        return jsonify({"success": True, "message": "消息监听已停止"})

    except Exception as e:
        logger.error(f"停止Bot失败: {e}", "API接口")
        return jsonify({"success": False, "message": f"停止失败: {str(e)}"}), 500

@app.route('/api/message/send', methods=['POST'])
def api_send_message():
    """发送消息"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "请求数据为空"}), 400

        user_name = data.get('user_name')
        message = data.get('message')

        if not user_name or not message:
            return jsonify({"success": False, "message": "用户名和消息内容不能为空"}), 400

        if not wx:
            return jsonify({"success": False, "message": "微信未初始化"}), 500

        wx.SendMsg(message, who=user_name)
        logger.info(f"发送消息给 {user_name}: {message}", "API接口")

        # 记录机器人发送的消息到日志
        try:
            # 查找目标用户的监听配置
            listen_list = config.get('LISTEN_LIST', [])
            target_listener = None

            for listener in listen_list:
                if listener.get('name') == user_name:
                    target_listener = listener
                    break

            if target_listener:
                user_type = target_listener.get('type', 'private')
                robot_name = ROBOT_WX_NAME or "机器人"
                # 直接传递消息内容，save_message_to_file会自动添加格式
                save_message_to_file(user_name, user_type, robot_name, message, "text")
                logger.debug(f"机器人发送的消息已记录到日志: {user_name}")
        except Exception as e:
            logger.warning(f"记录机器人发送消息失败: {e}", "消息发送")

        return jsonify({"success": True, "message": "消息发送成功"})

    except Exception as e:
        logger.error(f"发送消息失败: {e}", "API接口")
        return jsonify({"success": False, "message": f"发送失败: {str(e)}"}), 500

@app.route('/api/file/send', methods=['POST'])
def api_send_file():
    """发送文件"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "请求数据为空"}), 400

        user_name = data.get('user_name')
        file_path = data.get('file_path')

        if not user_name or not file_path:
            return jsonify({"success": False, "message": "用户名和文件路径不能为空"}), 400

        if not wx:
            return jsonify({"success": False, "message": "微信未初始化"}), 500

        if not os.path.exists(file_path):
            return jsonify({"success": False, "message": f"文件不存在: {file_path}"}), 400

        wx.SendFiles(file_path, who=user_name)
        logger.info(f"发送文件给 {user_name}: {file_path}", "API接口")

        # bot发送文件后，同步保存到wxautox文件下载目录
        sync_bot_sent_file_to_download_dir(file_path)

        return jsonify({"success": True, "message": "文件发送成功"})

    except Exception as e:
        logger.error(f"发送文件失败: {e}", "API接口")
        return jsonify({"success": False, "message": f"发送失败: {str(e)}"}), 500

@app.route('/api/listeners', methods=['GET'])
def api_get_listeners():
    """获取监听列表"""
    try:
        listeners = config.get('LISTEN_LIST', [])
        return jsonify({
            "success": True,
            "data": {
                "listeners": listeners,
                "count": len(listeners)
            }
        })
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/listeners/add', methods=['POST'])
def api_add_listener():
    """添加监听对象"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "请求数据为空"}), 400

        user_name = data.get('user_name')
        user_type = data.get('user_type', 'private')  # private 或 group

        if not user_name:
            return jsonify({"success": False, "message": "用户名不能为空"}), 400

        if user_type not in ['private', 'group']:
            return jsonify({"success": False, "message": "用户类型必须是 private 或 group"}), 400

        listen_list = config.get('LISTEN_LIST', [])

        # 检查是否已存在
        for listener in listen_list:
            if listener['name'] == user_name:
                return jsonify({"success": False, "message": f"监听对象 {user_name} 已存在"})

        # 添加到配置
        new_listener = {"name": user_name, "type": user_type}
        listen_list.append(new_listener)
        config['LISTEN_LIST'] = listen_list
        save_config(config)

        # 初始化监听状态
        current_time = datetime.now()
        with listener_lock:
            listener_status[user_name] = {
                'user_name': user_name,
                'user_type': user_type,
                'is_active': False,
                'last_message_time': None,
                'message_count': 0,
                'listener_added_time': current_time.isoformat(),
                'status': 'waiting'
            }

        # 如果Bot正在运行，立即添加监听
        if bot_running and wx:
            try:
                wx.AddListenChat(nickname=user_name, callback=message_listener)
                logger.info(f"已添加监听用户: {user_name} ({user_type})", "API接口")

                with listener_lock:
                    listener_status[user_name]['status'] = 'listening'

            except Exception as e:
                logger.error(f"添加监听用户失败: {e}", "API接口")
                with listener_lock:
                    listener_status[user_name]['status'] = 'error'
                return jsonify({"success": False, "message": f"添加监听失败: {str(e)}"}), 500

        logger.info(f"成功添加监听对象: {user_name} ({user_type})", "API接口")
        return jsonify({"success": True, "message": f"成功添加监听对象: {user_name} ({user_type})"})

    except Exception as e:
        logger.error(f"添加监听对象失败: {e}", "API接口")
        return jsonify({"success": False, "message": f"添加失败: {str(e)}"}), 500

@app.route('/api/listeners/remove', methods=['POST'])
def api_remove_listener():
    """移除监听对象"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "请求数据为空"}), 400

        user_name = data.get('user_name')
        if not user_name:
            return jsonify({"success": False, "message": "用户名不能为空"}), 400

        listen_list = config.get('LISTEN_LIST', [])

        # 查找并移除
        found = False
        removed_listener = None
        for i, listener in enumerate(listen_list):
            if listener['name'] == user_name:
                removed_listener = listen_list.pop(i)
                found = True
                break

        if not found:
            return jsonify({"success": False, "message": f"监听对象 {user_name} 不存在"})

        config['LISTEN_LIST'] = listen_list
        save_config(config)

        # 如果Bot正在运行，立即移除监听
        if bot_running and wx:
            try:
                wx.RemoveListenChat(nickname=user_name)
                logger.info(f"已移除监听用户: {user_name}")
            except Exception as e:
                logger.error(f"移除监听用户失败: {e}")

        # 移除监听状态
        with listener_lock:
            if user_name in listener_status:
                del listener_status[user_name]

        logger.info(f"成功移除监听对象: {user_name}")
        return jsonify({
            "success": True,
            "message": f"成功移除监听对象: {user_name}",
            "data": {
                "removed_listener": removed_listener,
                "remaining_count": len(listen_list)
            }
        })

    except Exception as e:
        logger.error(f"移除监听对象失败: {e}")
        return jsonify({"success": False, "message": f"移除失败: {str(e)}"}), 500

@app.route('/api/listeners/clear', methods=['POST'])
def api_clear_listeners():
    """清空所有监听对象"""
    try:
        listen_list = config.get('LISTEN_LIST', [])
        removed_count = len(listen_list)

        if removed_count == 0:
            return jsonify({"success": False, "message": "监听列表已为空"})

        # 清空配置
        config['LISTEN_LIST'] = []
        save_config(config)

        # 如果Bot正在运行，移除所有监听
        if bot_running and wx:
            for listener in listen_list:
                user_name = listener['name']
                try:
                    wx.RemoveListenChat(nickname=user_name)
                    logger.info(f"已移除监听用户: {user_name}")
                except Exception as e:
                    logger.error(f"移除监听用户 {user_name} 失败: {e}")

        # 清空监听状态
        with listener_lock:
            listener_status.clear()

        logger.info(f"成功清空所有监听对象，共移除 {removed_count} 个")
        return jsonify({
            "success": True,
            "message": f"成功清空所有监听对象，共移除 {removed_count} 个",
            "data": {
                "removed_count": removed_count,
                "removed_listeners": listen_list
            }
        })

    except Exception as e:
        logger.error(f"清空监听对象失败: {e}")
        return jsonify({"success": False, "message": f"清空失败: {str(e)}"}), 500

@app.route('/api/listeners/status', methods=['GET'])
def api_get_listeners_status():
    """获取监听对象状态"""
    try:
        current_time = datetime.now()

        with listener_lock:
            status_list = []

            for user_name, status in listener_status.items():
                # 计算活跃状态
                last_msg_time = status.get('last_message_time')
                if last_msg_time:
                    last_time = datetime.fromisoformat(last_msg_time.replace('Z', '+00:00').replace('+00:00', ''))
                    inactive_minutes = (current_time - last_time).total_seconds() / 60

                    if inactive_minutes > 30:
                        status['status'] = 'inactive'
                        status['is_active'] = False
                    else:
                        status['is_active'] = True

                # 添加额外信息
                status_info = status.copy()
                status_info['inactive_minutes'] = 0

                if last_msg_time:
                    last_time = datetime.fromisoformat(last_msg_time.replace('Z', '+00:00').replace('+00:00', ''))
                    status_info['inactive_minutes'] = round((current_time - last_time).total_seconds() / 60, 1)

                status_list.append(status_info)

        return jsonify({
            "success": True,
            "data": {
                "listeners": status_list,
                "total_listeners": len(status_list),
                "active_listeners": len([s for s in status_list if s['is_active']]),
                "inactive_listeners": len([s for s in status_list if not s['is_active']]),
                "check_time": current_time.isoformat()
            }
        })

    except Exception as e:
        logger.error(f"获取监听状态失败: {e}")
        return jsonify({"success": False, "message": f"获取失败: {str(e)}"}), 500

@app.route('/api/logs/list', methods=['GET'])
def api_list_log_files():
    """获取日志文件列表"""
    try:
        log_dir = config.get("LOG_DIR", "chat_logs")

        if not os.path.exists(log_dir):
            return jsonify({
                "success": True,
                "data": {
                    "private_chats": [],
                    "group_chats": [],
                    "total_files": 0
                }
            })

        private_chats = []
        group_chats = []

        # 扫描私聊文件夹
        private_dir = os.path.join(log_dir, "私聊")
        if os.path.exists(private_dir):
            for user_folder in os.listdir(private_dir):
                user_path = os.path.join(private_dir, user_folder)
                if os.path.isdir(user_path):
                    files = []
                    for file_name in os.listdir(user_path):
                        if file_name.endswith('.json'):
                            file_path = os.path.join(user_path, file_name)
                            file_size = os.path.getsize(file_path)
                            file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat()
                            files.append({
                                "name": file_name,
                                "size": file_size,
                                "modified_time": file_mtime
                            })

                    private_chats.append({
                        "user_name": user_folder,
                        "type": "private",
                        "files": files,
                        "file_count": len(files)
                    })

        # 扫描群聊文件夹
        group_dir = os.path.join(log_dir, "群聊")
        if os.path.exists(group_dir):
            for group_folder in os.listdir(group_dir):
                group_path = os.path.join(group_dir, group_folder)
                if os.path.isdir(group_path):
                    files = []
                    for file_name in os.listdir(group_path):
                        if file_name.endswith('.json'):
                            file_path = os.path.join(group_path, file_name)
                            file_size = os.path.getsize(file_path)
                            file_mtime = datetime.fromtimestamp(os.path.getmtime(file_path)).isoformat()
                            files.append({
                                "name": file_name,
                                "size": file_size,
                                "modified_time": file_mtime
                            })

                    group_chats.append({
                        "user_name": group_folder,
                        "type": "group",
                        "files": files,
                        "file_count": len(files)
                    })

        total_files = sum(chat['file_count'] for chat in private_chats + group_chats)

        return jsonify({
            "success": True,
            "data": {
                "private_chats": private_chats,
                "group_chats": group_chats,
                "total_files": total_files,
                "log_directory": log_dir
            }
        })

    except Exception as e:
        logger.error(f"获取日志文件列表失败: {e}")
        return jsonify({"success": False, "message": f"获取失败: {str(e)}"}), 500

@app.route('/api/logs/read', methods=['GET'])
def api_read_log_file():
    """读取日志文件内容 - 优化版，支持JSON格式和人性化格式输出"""
    try:
        user_name = request.args.get('user_name')
        user_type = request.args.get('user_type', 'private')
        date = request.args.get('date', datetime.now().strftime("%Y-%m-%d"))
        count = request.args.get('count', 20, type=int)  # 改为count，表示最近N条消息
        format_output = request.args.get('format', 'true').lower() == 'true'  # 是否格式化输出

        if not user_name:
            return jsonify({"success": False, "message": "用户名不能为空"}), 400

        log_dir = config.get("LOG_DIR", "chat_logs")

        if user_type == "group":
            file_path = os.path.join(log_dir, "群聊", user_name, f"{date}.json")
        else:
            file_path = os.path.join(log_dir, "私聊", user_name, f"{date}.json")

        if not os.path.exists(file_path):
            # 如果指定日期的文件不存在，尝试查找最近的文件
            user_dir = os.path.dirname(file_path)
            if os.path.exists(user_dir):
                files = [f for f in os.listdir(user_dir) if f.endswith('.json')]
                if files:
                    # 按日期排序，取最新的
                    files.sort(reverse=True)
                    latest_file = files[0]
                    file_path = os.path.join(user_dir, latest_file)
                    date = latest_file.replace('.json', '')
                else:
                    return jsonify({
                        "success": False,
                        "message": f"用户 {user_name} 暂无聊天记录"
                    }), 404
            else:
                return jsonify({
                    "success": False,
                    "message": f"用户 {user_name} 暂无聊天记录"
                }), 404

        # 读取JSON文件内容
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                all_messages = json.load(f)
        except json.JSONDecodeError:
            return jsonify({
                "success": False,
                "message": f"文件格式错误，无法解析JSON: {file_path}"
            }), 500
        except Exception as e:
            return jsonify({
                "success": False,
                "message": f"读取文件失败: {str(e)}"
            }), 500

        # 确保all_messages是列表
        if not isinstance(all_messages, list):
            all_messages = []

        # 获取最近N条消息
        if count > 0:
            recent_messages = all_messages[-count:]
        else:
            recent_messages = all_messages

        # 格式化输出
        if format_output:
            # 生成人性化的标题
            if user_type == "group":
                title = f"以下是群聊「{user_name}」的历史记录"
                if date != datetime.now().strftime("%Y-%m-%d"):
                    title += f"（{date}）"
            else:
                robot_name = ROBOT_WX_NAME or "机器人"
                title = f"以下是{robot_name}和{user_name}的私聊记录"
                if date != datetime.now().strftime("%Y-%m-%d"):
                    title += f"（{date}）"

            # 统计信息
            stats = f"共 {len(recent_messages)} 条消息"
            if len(recent_messages) < len(all_messages):
                stats += f"（显示最近 {len(recent_messages)} 条，总共 {len(all_messages)} 条）"

            # 组装格式化内容
            formatted_content = f"{title}\n{stats}\n{'='*50}\n\n"

            # 添加消息内容，并编号
            for i, msg in enumerate(recent_messages, 1):
                time_str = msg.get('time_str', '未知时间')
                content_text = msg.get('content', '')
                formatted_content += f"{i:2d}. [{time_str}] {content_text}\n"

            formatted_content += f"\n{'='*50}\n记录时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            content = formatted_content
        else:
            # 原始JSON格式输出
            content = json.dumps(recent_messages, ensure_ascii=False, indent=2)

        # 分析消息类型统计
        message_stats = {
            "text": 0,
            "image": 0,
            "voice": 0,
            "file": 0,
            "link": 0,
            "other": 0
        }

        # 基于JSON消息数据进行统计
        for msg in recent_messages:
            msg_type = msg.get('type', 'other')
            if msg_type in message_stats:
                message_stats[msg_type] += 1
            else:
                message_stats["other"] += 1

        return jsonify({
            "success": True,
            "data": {
                "user_name": user_name,
                "user_type": user_type,
                "date": date,
                "file_path": file_path,
                "total_messages": len(all_messages),
                "returned_messages": len(recent_messages),
                "content": content,
                "formatted": format_output,
                "message_stats": message_stats,
                "summary": {
                    "title": f"{'群聊' if user_type == 'group' else '私聊'}「{user_name}」",
                    "date_range": date,
                    "message_count": len(recent_messages),
                    "file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0
                }
            }
        })

    except Exception as e:
        logger.error(f"读取日志文件失败: {e}")
        return jsonify({"success": False, "message": f"读取失败: {str(e)}"}), 500

@app.route('/api/config', methods=['GET'])
def api_get_config():
    """获取当前配置"""
    try:
        return jsonify({
            "success": True,
            "data": config
        })
    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/config', methods=['POST'])
def api_update_config():
    """更新配置并热重载"""
    global config
    try:
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "message": "请求数据为空"}), 400

        # 更新配置
        config.update(data)

        # 保存配置到文件
        if save_config(config):
            logger.info("配置已更新并保存")
            return jsonify({
                "success": True,
                "message": "配置更新成功",
                "data": config
            })
        else:
            return jsonify({"success": False, "message": "配置保存失败"}), 500

    except Exception as e:
        logger.error(f"更新配置失败: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/config/reload', methods=['POST'])
def api_reload_config():
    """重新加载配置文件并同步监听器"""
    global config
    try:
        # 保存旧的监听列表
        old_listen_list = config.get('LISTEN_LIST', [])
        old_listeners = {listener['name']: listener for listener in old_listen_list}

        # 重新加载配置
        config = load_config()
        new_listen_list = config.get('LISTEN_LIST', [])
        new_listeners = {listener['name']: listener for listener in new_listen_list}

        logger.info(f"手动重新加载配置完成，旧监听器: {len(old_listeners)}个，新监听器: {len(new_listeners)}个", "API接口")

        # 如果Bot正在运行，同步更新监听器
        sync_result = {"updated": False, "details": "Bot未运行，监听器将在下次启动时生效"}
        if bot_running and wx:
            try:
                # 使用配置热更新管理器的同步逻辑
                config_hot_reloader.sync_listeners(old_listeners, new_listeners)
                sync_result = {"updated": True, "details": "监听器已同步更新"}
            except Exception as sync_error:
                logger.error(f"同步监听器失败: {sync_error}", "API接口")
                sync_result = {"updated": False, "details": f"监听器同步失败: {str(sync_error)}"}

        return jsonify({
            "success": True,
            "message": "配置重新加载成功",
            "data": {
                "config": config,
                "listener_sync": sync_result,
                "old_listeners_count": len(old_listeners),
                "new_listeners_count": len(new_listeners)
            }
        })
    except Exception as e:
        logger.error(f"重新加载配置失败: {e}", "API接口")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/robot/info', methods=['GET'])
def api_get_robot_info():
    """获取机器人信息"""
    try:
        if not wx:
            return jsonify({"success": False, "message": "微信未初始化"}), 500

        info = wx.GetMyInfo()
        return jsonify({
            "success": True,
            "data": info
        })

    except Exception as e:
        logger.error(f"获取机器人信息失败: {e}")
        return jsonify({"success": False, "message": f"获取失败: {str(e)}"}), 500

# 消息缓存管理API
@app.route('/api/cache/stats', methods=['GET'])
def api_get_cache_stats():
    """获取缓存统计信息"""
    try:
        stats = get_cache_statistics()
        return jsonify({
            "success": True,
            "data": stats
        })
    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}")
        return jsonify({"success": False, "message": f"获取失败: {str(e)}"}), 500

@app.route('/api/cache/cleanup', methods=['POST'])
def api_cleanup_cache():
    """手动清理缓存"""
    try:
        cleanup_old_cache_entries()
        return jsonify({
            "success": True,
            "message": "缓存清理完成"
        })
    except Exception as e:
        logger.error(f"清理缓存失败: {e}")
        return jsonify({"success": False, "message": f"清理失败: {str(e)}"}), 500

@app.route('/api/cache/reset', methods=['POST'])
def api_reset_cache_stats():
    """重置缓存统计"""
    try:
        reset_cache_statistics()
        return jsonify({
            "success": True,
            "message": "缓存统计已重置"
        })
    except Exception as e:
        logger.error(f"重置统计失败: {e}")
        return jsonify({"success": False, "message": f"重置失败: {str(e)}"}), 500

@app.route('/api/cache/status', methods=['GET'])
def api_get_cache_status():
    """获取缓存状态详情"""
    try:
        ai_config = config.get('AI_AUTO_REPLY', {})
        cache_config = ai_config.get('message_cache', {})

        with cache_lock:
            cache_details = {}
            for cache_id, cache_entry in message_cache.items():
                cache_details[cache_id] = {
                    'queued_messages': len(cache_entry['messages']),
                    'processing': cache_entry['processing'],
                    'wait_cycles': cache_entry['wait_cycles'],
                    'created_time': cache_entry['created_time'],
                    'last_activity': cache_entry['last_activity'],
                    'total_messages': cache_entry['total_messages'],
                    'processed_batches': cache_entry['processed_batches'],
                    'has_timer': cache_entry['timer'] is not None
                }

        return jsonify({
            "success": True,
            "data": {
                "config": cache_config,
                "cache_details": cache_details,
                "total_active_caches": len(cache_details)
            }
        })
    except Exception as e:
        logger.error(f"获取缓存状态失败: {e}")
        return jsonify({"success": False, "message": f"获取失败: {str(e)}"}), 500

@app.route('/api/cache/config/reload', methods=['POST'])
def api_reload_cache_config():
    """重新加载缓存配置"""
    try:
        # 重新加载配置文件
        load_config()

        # 获取新的缓存配置
        ai_config = config.get('AI_AUTO_REPLY', {})
        cache_config = ai_config.get('message_cache', {})

        logger.info("缓存配置已重新加载", "消息缓存")

        return jsonify({
            "success": True,
            "message": "缓存配置已重新加载",
            "data": {
                "config": cache_config
            }
        })
    except Exception as e:
        logger.error(f"重新加载缓存配置失败: {e}")
        return jsonify({"success": False, "message": f"重新加载失败: {str(e)}"}), 500

# 定期清理任务
def start_cache_cleanup_task():
    """启动定期缓存清理任务"""
    def cleanup_task():
        while True:
            try:
                time.sleep(1800)  # 每30分钟清理一次
                cleanup_old_cache_entries()
            except Exception as e:
                logger.error(f"定期清理任务失败: {e}", "消息缓存")

    cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
    cleanup_thread.start()
    logger.info("缓存清理任务已启动，每30分钟执行一次", "消息缓存")

# 主函数
def main():
    """主函数"""
    global bot_running

    logger.info("微信机器人完整API服务启动")
    logger.info("版本: 2.0.0-integrated-with-cache")
    logger.info("功能: 消息监听记录 + 消息发送 + 文件管理 + 自动启动 + 智能消息缓存")

    # 在主线程中初始化微信
    if not init_wechat():
        logger.error("微信初始化失败，程序退出")
        return

    # 启动缓存清理任务
    start_cache_cleanup_task()

    # 创建日志目录
    log_dir = config.get("LOG_DIR", "chat_logs")
    os.makedirs(os.path.join(log_dir, "私聊"), exist_ok=True)
    os.makedirs(os.path.join(log_dir, "群聊"), exist_ok=True)
    logger.info(f"日志目录: {os.path.abspath(log_dir)}")

    # 启动配置文件热更新监控
    config_hot_reloader.start_monitoring()

    # 自动启动监听
    auto_start_listening()

    # 启动Flask应用
    try:
        port = config.get("WEB_PORT", 7702)
        logger.info(f"启动HTTP API服务器，端口: {port}")
        logger.info(f"API状态地址: http://localhost:{port}/api/status")

        if bot_running:
            logger.info("✅ 消息监听已自动启动")
        else:
            logger.info("⚠️ 消息监听未启动，可通过API手动启动")

        app.run(host='0.0.0.0', port=port, debug=False, threaded=True)

    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
        if bot_running:
            bot_running = False
    except Exception as e:
        logger.error(f"程序运行错误: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()
