# NovelAI图片生成插件配置示例

# 必需配置
YUANPLUS_API_KEY=your_yuanplus_api_key_here

# 角色外貌特征配置
ENABLE_CHARACTER_APPEARANCE=true
DEFAULT_CHARACTER_APPEARANCE=best quality, ultra-detailed, absurdres, 1girl, anime style

# 角色外貌配置示例（格式：角色名_CHARACTER_APPEARANCE）
# 当用户要求画特定角色时，系统会自动查找对应的外貌配置
ALICE_CHARACTER_APPEARANCE=1girl, blonde hair, blue eyes, school uniform, cute smile
BOB_CHARACTER_APPEARANCE=1boy, brown hair, green eyes, casual clothes, friendly expression
MIKU_CHARACTER_APPEARANCE=1girl, long turquoise twin tails, blue eyes, futuristic outfit, vocaloid
SAKURA_CHARACTER_APPEARANCE=1girl, pink hair, green eyes, magical girl outfit, cherry blossoms

# 使用说明：
# 1. 当用户说"画一张Alice的照片"时，设置include_character=true，character_names=["ALICE"]
# 2. 系统会自动查找ALICE_CHARACTER_APPEARANCE配置并添加到提示词中
# 3. 支持多个角色：character_names=["ALICE", "BOB"]会同时使用两个角色的外貌特征
# 4. 如果找不到特定角色配置，会使用DEFAULT_CHARACTER_APPEARANCE作为备用