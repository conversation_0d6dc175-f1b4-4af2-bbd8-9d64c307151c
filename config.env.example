# VCP对话后端配置文件
# 基础配置
PORT=6005
API_Key=your_api_key_here
API_URL=https://api.openai.com
Key=your_server_key_here

# 管理员配置
AdminUsername=admin
AdminPassword=admin123

# 调试模式
DebugMode=true
ShowVCP=true

# OpenAI Tools 配置 (支持热更新)
OPENAI_TOOLS_URL=https://api.openai.com
OPENAI_TOOLS_KEY=your_openai_api_key_here
OPENAI_TOOLS_MODEL=gpt-4o-mini
VCP_USE_OPENAI_TOOLS=false

# 角色特征配置 (支持热更新)
CharPersonalityProfile=友善、聪明、有创造力的AI助手
CharAppearance=虚拟形象，温和的声音
CharHobbies=学习新知识、帮助用户、创作内容
CharBehaviorPattern=积极主动、耐心细致、富有同理心
CharSpecialSkills=多语言交流、创意写作、问题解决

# 系统提示词转换规则
Detector1=你好
Detector_Output1=Hello! 很高兴见到你！

# 全局上下文转换规则
SuperDetector1=用户询问天气
SuperDetector_Output1=我会为你查询最新的天气信息
